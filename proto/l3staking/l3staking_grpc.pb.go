// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v4.25.3
// source: dapplink/l3staking.proto

package l3staking

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	L3StakingService_UpdateStakingNodeIncome_FullMethodName = "/services.dapplink.l3staking.L3StakingService/updateStakingNodeIncome"
)

// L3StakingServiceClient is the client API for L3StakingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type L3StakingServiceClient interface {
	UpdateStakingNodeIncome(ctx context.Context, in *StakingNodeReq, opts ...grpc.CallOption) (*StakingNodeRep, error)
}

type l3StakingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewL3StakingServiceClient(cc grpc.ClientConnInterface) L3StakingServiceClient {
	return &l3StakingServiceClient{cc}
}

func (c *l3StakingServiceClient) UpdateStakingNodeIncome(ctx context.Context, in *StakingNodeReq, opts ...grpc.CallOption) (*StakingNodeRep, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StakingNodeRep)
	err := c.cc.Invoke(ctx, L3StakingService_UpdateStakingNodeIncome_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// L3StakingServiceServer is the server API for L3StakingService service.
// All implementations should embed UnimplementedL3StakingServiceServer
// for forward compatibility.
type L3StakingServiceServer interface {
	UpdateStakingNodeIncome(context.Context, *StakingNodeReq) (*StakingNodeRep, error)
}

// UnimplementedL3StakingServiceServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedL3StakingServiceServer struct{}

func (UnimplementedL3StakingServiceServer) UpdateStakingNodeIncome(context.Context, *StakingNodeReq) (*StakingNodeRep, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateStakingNodeIncome not implemented")
}
func (UnimplementedL3StakingServiceServer) testEmbeddedByValue() {}

// UnsafeL3StakingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to L3StakingServiceServer will
// result in compilation errors.
type UnsafeL3StakingServiceServer interface {
	mustEmbedUnimplementedL3StakingServiceServer()
}

func RegisterL3StakingServiceServer(s grpc.ServiceRegistrar, srv L3StakingServiceServer) {
	// If the following call pancis, it indicates UnimplementedL3StakingServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&L3StakingService_ServiceDesc, srv)
}

func _L3StakingService_UpdateStakingNodeIncome_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StakingNodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(L3StakingServiceServer).UpdateStakingNodeIncome(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: L3StakingService_UpdateStakingNodeIncome_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(L3StakingServiceServer).UpdateStakingNodeIncome(ctx, req.(*StakingNodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

// L3StakingService_ServiceDesc is the grpc.ServiceDesc for L3StakingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var L3StakingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "services.dapplink.l3staking.L3StakingService",
	HandlerType: (*L3StakingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "updateStakingNodeIncome",
			Handler:    _L3StakingService_UpdateStakingNodeIncome_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "dapplink/l3staking.proto",
}
