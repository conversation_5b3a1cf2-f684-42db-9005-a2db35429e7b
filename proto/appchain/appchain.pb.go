// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v4.25.3
// source: dapplink/appchain.proto

package appchain

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type L1StakerRewardsAmountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChainId       string                 `protobuf:"bytes,1,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"`
	StakerAddress string                 `protobuf:"bytes,2,opt,name=staker_address,json=stakerAddress,proto3" json:"staker_address,omitempty"`
	Strategies    string                 `protobuf:"bytes,3,opt,name=strategies,proto3" json:"strategies,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *L1StakerRewardsAmountRequest) Reset() {
	*x = L1StakerRewardsAmountRequest{}
	mi := &file_dapplink_appchain_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *L1StakerRewardsAmountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*L1StakerRewardsAmountRequest) ProtoMessage() {}

func (x *L1StakerRewardsAmountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_appchain_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use L1StakerRewardsAmountRequest.ProtoReflect.Descriptor instead.
func (*L1StakerRewardsAmountRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_appchain_proto_rawDescGZIP(), []int{0}
}

func (x *L1StakerRewardsAmountRequest) GetChainId() string {
	if x != nil {
		return x.ChainId
	}
	return ""
}

func (x *L1StakerRewardsAmountRequest) GetStakerAddress() string {
	if x != nil {
		return x.StakerAddress
	}
	return ""
}

func (x *L1StakerRewardsAmountRequest) GetStrategies() string {
	if x != nil {
		return x.Strategies
	}
	return ""
}

type L1StakerRewardsAmountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Income        string                 `protobuf:"bytes,3,opt,name=income,proto3" json:"income,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *L1StakerRewardsAmountResponse) Reset() {
	*x = L1StakerRewardsAmountResponse{}
	mi := &file_dapplink_appchain_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *L1StakerRewardsAmountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*L1StakerRewardsAmountResponse) ProtoMessage() {}

func (x *L1StakerRewardsAmountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_appchain_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use L1StakerRewardsAmountResponse.ProtoReflect.Descriptor instead.
func (*L1StakerRewardsAmountResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_appchain_proto_rawDescGZIP(), []int{1}
}

func (x *L1StakerRewardsAmountResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *L1StakerRewardsAmountResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *L1StakerRewardsAmountResponse) GetIncome() string {
	if x != nil {
		return x.Income
	}
	return ""
}

type L2StakerRewardsAmountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChainId       string                 `protobuf:"bytes,1,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"`
	StakerAddress string                 `protobuf:"bytes,2,opt,name=staker_address,json=stakerAddress,proto3" json:"staker_address,omitempty"`
	Strategy      string                 `protobuf:"bytes,3,opt,name=strategy,proto3" json:"strategy,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *L2StakerRewardsAmountRequest) Reset() {
	*x = L2StakerRewardsAmountRequest{}
	mi := &file_dapplink_appchain_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *L2StakerRewardsAmountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*L2StakerRewardsAmountRequest) ProtoMessage() {}

func (x *L2StakerRewardsAmountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_appchain_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use L2StakerRewardsAmountRequest.ProtoReflect.Descriptor instead.
func (*L2StakerRewardsAmountRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_appchain_proto_rawDescGZIP(), []int{2}
}

func (x *L2StakerRewardsAmountRequest) GetChainId() string {
	if x != nil {
		return x.ChainId
	}
	return ""
}

func (x *L2StakerRewardsAmountRequest) GetStakerAddress() string {
	if x != nil {
		return x.StakerAddress
	}
	return ""
}

func (x *L2StakerRewardsAmountRequest) GetStrategy() string {
	if x != nil {
		return x.Strategy
	}
	return ""
}

type L2StakerRewardsAmountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Income        string                 `protobuf:"bytes,3,opt,name=income,proto3" json:"income,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *L2StakerRewardsAmountResponse) Reset() {
	*x = L2StakerRewardsAmountResponse{}
	mi := &file_dapplink_appchain_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *L2StakerRewardsAmountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*L2StakerRewardsAmountResponse) ProtoMessage() {}

func (x *L2StakerRewardsAmountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_appchain_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use L2StakerRewardsAmountResponse.ProtoReflect.Descriptor instead.
func (*L2StakerRewardsAmountResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_appchain_proto_rawDescGZIP(), []int{3}
}

func (x *L2StakerRewardsAmountResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *L2StakerRewardsAmountResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *L2StakerRewardsAmountResponse) GetIncome() string {
	if x != nil {
		return x.Income
	}
	return ""
}

var File_dapplink_appchain_proto protoreflect.FileDescriptor

var file_dapplink_appchain_proto_rawDesc = string([]byte{
	0x0a, 0x17, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x61, 0x70, 0x70, 0x63, 0x68,
	0x61, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x70, 0x70,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x22, 0x80, 0x01, 0x0a, 0x1c, 0x4c, 0x31, 0x53, 0x74, 0x61, 0x6b,
	0x65, 0x72, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49,
	0x64, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x6b, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x6b, 0x65,
	0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x72, 0x61,
	0x74, 0x65, 0x67, 0x69, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x65, 0x67, 0x69, 0x65, 0x73, 0x22, 0x6b, 0x0a, 0x1d, 0x4c, 0x31, 0x53, 0x74,
	0x61, 0x6b, 0x65, 0x72, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x69,
	0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x22, 0x7c, 0x0a, 0x1c, 0x4c, 0x32, 0x53, 0x74, 0x61, 0x6b, 0x65,
	0x72, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x64,
	0x12, 0x25, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x6b, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x6b, 0x65, 0x72,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x65, 0x67, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x65, 0x67, 0x79, 0x22, 0x6b, 0x0a, 0x1d, 0x4c, 0x32, 0x53, 0x74, 0x61, 0x6b, 0x65, 0x72, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6e, 0x63, 0x6f,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65,
	0x32, 0xaf, 0x02, 0x0a, 0x0f, 0x41, 0x70, 0x70, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x8c, 0x01, 0x0a, 0x15, 0x4c, 0x31, 0x53, 0x74, 0x61, 0x6b, 0x65,
	0x72, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x38,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x61, 0x70, 0x70, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x4c, 0x31, 0x53, 0x74,
	0x61, 0x6b, 0x65, 0x72, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x70, 0x70,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x4c, 0x31, 0x53, 0x74, 0x61, 0x6b, 0x65, 0x72, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x8c, 0x01, 0x0a, 0x15, 0x4c, 0x32, 0x53, 0x74, 0x61, 0x6b, 0x65, 0x72,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x38, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x61, 0x70, 0x70, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x4c, 0x32, 0x53, 0x74, 0x61,
	0x6b, 0x65, 0x72, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x70, 0x70, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x2e, 0x4c, 0x32, 0x53, 0x74, 0x61, 0x6b, 0x65, 0x72, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x42, 0x29, 0x0a, 0x15, 0x78, 0x79, 0x7a, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x61, 0x70, 0x70, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5a, 0x10, 0x2e, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x70, 0x70, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_dapplink_appchain_proto_rawDescOnce sync.Once
	file_dapplink_appchain_proto_rawDescData []byte
)

func file_dapplink_appchain_proto_rawDescGZIP() []byte {
	file_dapplink_appchain_proto_rawDescOnce.Do(func() {
		file_dapplink_appchain_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_dapplink_appchain_proto_rawDesc), len(file_dapplink_appchain_proto_rawDesc)))
	})
	return file_dapplink_appchain_proto_rawDescData
}

var file_dapplink_appchain_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_dapplink_appchain_proto_goTypes = []any{
	(*L1StakerRewardsAmountRequest)(nil),  // 0: services.dapplink.appchain.L1StakerRewardsAmountRequest
	(*L1StakerRewardsAmountResponse)(nil), // 1: services.dapplink.appchain.L1StakerRewardsAmountResponse
	(*L2StakerRewardsAmountRequest)(nil),  // 2: services.dapplink.appchain.L2StakerRewardsAmountRequest
	(*L2StakerRewardsAmountResponse)(nil), // 3: services.dapplink.appchain.L2StakerRewardsAmountResponse
}
var file_dapplink_appchain_proto_depIdxs = []int32{
	0, // 0: services.dapplink.appchain.AppChainService.L1StakerRewardsAmount:input_type -> services.dapplink.appchain.L1StakerRewardsAmountRequest
	2, // 1: services.dapplink.appchain.AppChainService.L2StakerRewardsAmount:input_type -> services.dapplink.appchain.L2StakerRewardsAmountRequest
	1, // 2: services.dapplink.appchain.AppChainService.L1StakerRewardsAmount:output_type -> services.dapplink.appchain.L1StakerRewardsAmountResponse
	3, // 3: services.dapplink.appchain.AppChainService.L2StakerRewardsAmount:output_type -> services.dapplink.appchain.L2StakerRewardsAmountResponse
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_dapplink_appchain_proto_init() }
func file_dapplink_appchain_proto_init() {
	if File_dapplink_appchain_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_dapplink_appchain_proto_rawDesc), len(file_dapplink_appchain_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_dapplink_appchain_proto_goTypes,
		DependencyIndexes: file_dapplink_appchain_proto_depIdxs,
		MessageInfos:      file_dapplink_appchain_proto_msgTypes,
	}.Build()
	File_dapplink_appchain_proto = out.File
	file_dapplink_appchain_proto_goTypes = nil
	file_dapplink_appchain_proto_depIdxs = nil
}
