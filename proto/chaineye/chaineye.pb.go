// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v4.25.3
// source: dapplink/chaineye.proto

package chaineye

import (
	common "./proto/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ArticleList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Title         string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Abstract      string                 `protobuf:"bytes,3,opt,name=abstract,proto3" json:"abstract,omitempty"`
	Type          int32                  `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`
	Author        string                 `protobuf:"bytes,5,opt,name=author,proto3" json:"author,omitempty"`
	Views         int32                  `protobuf:"varint,6,opt,name=views,proto3" json:"views,omitempty"`
	AddTime       string                 `protobuf:"bytes,7,opt,name=add_time,json=addTime,proto3" json:"add_time,omitempty"`
	UpdTime       string                 `protobuf:"bytes,8,opt,name=upd_time,json=updTime,proto3" json:"upd_time,omitempty"`
	Cover         string                 `protobuf:"bytes,9,opt,name=cover,proto3" json:"cover,omitempty"`
	Like          int32                  `protobuf:"varint,10,opt,name=like,proto3" json:"like,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ArticleList) Reset() {
	*x = ArticleList{}
	mi := &file_dapplink_chaineye_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArticleList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleList) ProtoMessage() {}

func (x *ArticleList) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_chaineye_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleList.ProtoReflect.Descriptor instead.
func (*ArticleList) Descriptor() ([]byte, []int) {
	return file_dapplink_chaineye_proto_rawDescGZIP(), []int{0}
}

func (x *ArticleList) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ArticleList) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ArticleList) GetAbstract() string {
	if x != nil {
		return x.Abstract
	}
	return ""
}

func (x *ArticleList) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *ArticleList) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *ArticleList) GetViews() int32 {
	if x != nil {
		return x.Views
	}
	return 0
}

func (x *ArticleList) GetAddTime() string {
	if x != nil {
		return x.AddTime
	}
	return ""
}

func (x *ArticleList) GetUpdTime() string {
	if x != nil {
		return x.UpdTime
	}
	return ""
}

func (x *ArticleList) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

func (x *ArticleList) GetLike() int32 {
	if x != nil {
		return x.Like
	}
	return 0
}

type CommentList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Detail        string                 `protobuf:"bytes,2,opt,name=detail,proto3" json:"detail,omitempty"`
	Author        string                 `protobuf:"bytes,3,opt,name=author,proto3" json:"author,omitempty"`
	AddTime       string                 `protobuf:"bytes,4,opt,name=add_time,json=addTime,proto3" json:"add_time,omitempty"`
	Views         int32                  `protobuf:"varint,5,opt,name=views,proto3" json:"views,omitempty"`
	Like          int32                  `protobuf:"varint,6,opt,name=like,proto3" json:"like,omitempty"`
	Reply         int32                  `protobuf:"varint,7,opt,name=reply,proto3" json:"reply,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CommentList) Reset() {
	*x = CommentList{}
	mi := &file_dapplink_chaineye_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommentList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommentList) ProtoMessage() {}

func (x *CommentList) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_chaineye_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommentList.ProtoReflect.Descriptor instead.
func (*CommentList) Descriptor() ([]byte, []int) {
	return file_dapplink_chaineye_proto_rawDescGZIP(), []int{1}
}

func (x *CommentList) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CommentList) GetDetail() string {
	if x != nil {
		return x.Detail
	}
	return ""
}

func (x *CommentList) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *CommentList) GetAddTime() string {
	if x != nil {
		return x.AddTime
	}
	return ""
}

func (x *CommentList) GetViews() int32 {
	if x != nil {
		return x.Views
	}
	return 0
}

func (x *CommentList) GetLike() int32 {
	if x != nil {
		return x.Like
	}
	return 0
}

func (x *CommentList) GetReply() int32 {
	if x != nil {
		return x.Reply
	}
	return 0
}

type LikeList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Asset         string                 `protobuf:"bytes,3,opt,name=asset,proto3" json:"asset,omitempty"`
	Amount        string                 `protobuf:"bytes,4,opt,name=amount,proto3" json:"amount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LikeList) Reset() {
	*x = LikeList{}
	mi := &file_dapplink_chaineye_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LikeList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LikeList) ProtoMessage() {}

func (x *LikeList) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_chaineye_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LikeList.ProtoReflect.Descriptor instead.
func (*LikeList) Descriptor() ([]byte, []int) {
	return file_dapplink_chaineye_proto_rawDescGZIP(), []int{2}
}

func (x *LikeList) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *LikeList) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LikeList) GetAsset() string {
	if x != nil {
		return x.Asset
	}
	return ""
}

func (x *LikeList) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

type CatList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CatList) Reset() {
	*x = CatList{}
	mi := &file_dapplink_chaineye_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CatList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CatList) ProtoMessage() {}

func (x *CatList) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_chaineye_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CatList.ProtoReflect.Descriptor instead.
func (*CatList) Descriptor() ([]byte, []int) {
	return file_dapplink_chaineye_proto_rawDescGZIP(), []int{3}
}

func (x *CatList) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CatList) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type AssetAddress struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Asset         string                 `protobuf:"bytes,1,opt,name=asset,proto3" json:"asset,omitempty"`
	Address       string                 `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssetAddress) Reset() {
	*x = AssetAddress{}
	mi := &file_dapplink_chaineye_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssetAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetAddress) ProtoMessage() {}

func (x *AssetAddress) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_chaineye_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetAddress.ProtoReflect.Descriptor instead.
func (*AssetAddress) Descriptor() ([]byte, []int) {
	return file_dapplink_chaineye_proto_rawDescGZIP(), []int{4}
}

func (x *AssetAddress) GetAsset() string {
	if x != nil {
		return x.Asset
	}
	return ""
}

func (x *AssetAddress) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type ArticleCatReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Type          int32                  `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ArticleCatReq) Reset() {
	*x = ArticleCatReq{}
	mi := &file_dapplink_chaineye_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArticleCatReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleCatReq) ProtoMessage() {}

func (x *ArticleCatReq) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_chaineye_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleCatReq.ProtoReflect.Descriptor instead.
func (*ArticleCatReq) Descriptor() ([]byte, []int) {
	return file_dapplink_chaineye_proto_rawDescGZIP(), []int{5}
}

func (x *ArticleCatReq) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *ArticleCatReq) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

type ArticleCatRep struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	CatList       []*CatList             `protobuf:"bytes,3,rep,name=cat_list,json=catList,proto3" json:"cat_list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ArticleCatRep) Reset() {
	*x = ArticleCatRep{}
	mi := &file_dapplink_chaineye_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArticleCatRep) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleCatRep) ProtoMessage() {}

func (x *ArticleCatRep) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_chaineye_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleCatRep.ProtoReflect.Descriptor instead.
func (*ArticleCatRep) Descriptor() ([]byte, []int) {
	return file_dapplink_chaineye_proto_rawDescGZIP(), []int{6}
}

func (x *ArticleCatRep) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *ArticleCatRep) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ArticleCatRep) GetCatList() []*CatList {
	if x != nil {
		return x.CatList
	}
	return nil
}

type ArticleListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Type          int32                  `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	CatId         string                 `protobuf:"bytes,3,opt,name=cat_id,json=catId,proto3" json:"cat_id,omitempty"`
	Page          uint32                 `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	Pagesize      uint32                 `protobuf:"varint,5,opt,name=pagesize,proto3" json:"pagesize,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ArticleListReq) Reset() {
	*x = ArticleListReq{}
	mi := &file_dapplink_chaineye_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArticleListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleListReq) ProtoMessage() {}

func (x *ArticleListReq) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_chaineye_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleListReq.ProtoReflect.Descriptor instead.
func (*ArticleListReq) Descriptor() ([]byte, []int) {
	return file_dapplink_chaineye_proto_rawDescGZIP(), []int{7}
}

func (x *ArticleListReq) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *ArticleListReq) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *ArticleListReq) GetCatId() string {
	if x != nil {
		return x.CatId
	}
	return ""
}

func (x *ArticleListReq) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ArticleListReq) GetPagesize() uint32 {
	if x != nil {
		return x.Pagesize
	}
	return 0
}

type ArticleListRep struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Total         int32                  `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	Articles      []*ArticleList         `protobuf:"bytes,4,rep,name=articles,proto3" json:"articles,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ArticleListRep) Reset() {
	*x = ArticleListRep{}
	mi := &file_dapplink_chaineye_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArticleListRep) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleListRep) ProtoMessage() {}

func (x *ArticleListRep) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_chaineye_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleListRep.ProtoReflect.Descriptor instead.
func (*ArticleListRep) Descriptor() ([]byte, []int) {
	return file_dapplink_chaineye_proto_rawDescGZIP(), []int{8}
}

func (x *ArticleListRep) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *ArticleListRep) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ArticleListRep) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ArticleListRep) GetArticles() []*ArticleList {
	if x != nil {
		return x.Articles
	}
	return nil
}

type ArticleDetailReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Type          int32                  `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	Id            string                 `protobuf:"bytes,3,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ArticleDetailReq) Reset() {
	*x = ArticleDetailReq{}
	mi := &file_dapplink_chaineye_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArticleDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleDetailReq) ProtoMessage() {}

func (x *ArticleDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_chaineye_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleDetailReq.ProtoReflect.Descriptor instead.
func (*ArticleDetailReq) Descriptor() ([]byte, []int) {
	return file_dapplink_chaineye_proto_rawDescGZIP(), []int{9}
}

func (x *ArticleDetailReq) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *ArticleDetailReq) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *ArticleDetailReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type ArticleDetailRep struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Title         string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Detail        string                 `protobuf:"bytes,4,opt,name=detail,proto3" json:"detail,omitempty"`
	AuthorId      string                 `protobuf:"bytes,5,opt,name=author_id,json=authorId,proto3" json:"author_id,omitempty"`
	Author        string                 `protobuf:"bytes,6,opt,name=author,proto3" json:"author,omitempty"`
	Views         int32                  `protobuf:"varint,7,opt,name=views,proto3" json:"views,omitempty"`
	AddTime       string                 `protobuf:"bytes,8,opt,name=add_time,json=addTime,proto3" json:"add_time,omitempty"`
	UpdTime       string                 `protobuf:"bytes,9,opt,name=upd_time,json=updTime,proto3" json:"upd_time,omitempty"`
	Like          int32                  `protobuf:"varint,10,opt,name=like,proto3" json:"like,omitempty"`
	Comments      []*CommentList         `protobuf:"bytes,11,rep,name=comments,proto3" json:"comments,omitempty"`
	Likes         []*LikeList            `protobuf:"bytes,12,rep,name=likes,proto3" json:"likes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ArticleDetailRep) Reset() {
	*x = ArticleDetailRep{}
	mi := &file_dapplink_chaineye_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArticleDetailRep) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleDetailRep) ProtoMessage() {}

func (x *ArticleDetailRep) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_chaineye_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleDetailRep.ProtoReflect.Descriptor instead.
func (*ArticleDetailRep) Descriptor() ([]byte, []int) {
	return file_dapplink_chaineye_proto_rawDescGZIP(), []int{10}
}

func (x *ArticleDetailRep) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *ArticleDetailRep) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ArticleDetailRep) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ArticleDetailRep) GetDetail() string {
	if x != nil {
		return x.Detail
	}
	return ""
}

func (x *ArticleDetailRep) GetAuthorId() string {
	if x != nil {
		return x.AuthorId
	}
	return ""
}

func (x *ArticleDetailRep) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *ArticleDetailRep) GetViews() int32 {
	if x != nil {
		return x.Views
	}
	return 0
}

func (x *ArticleDetailRep) GetAddTime() string {
	if x != nil {
		return x.AddTime
	}
	return ""
}

func (x *ArticleDetailRep) GetUpdTime() string {
	if x != nil {
		return x.UpdTime
	}
	return ""
}

func (x *ArticleDetailRep) GetLike() int32 {
	if x != nil {
		return x.Like
	}
	return 0
}

func (x *ArticleDetailRep) GetComments() []*CommentList {
	if x != nil {
		return x.Comments
	}
	return nil
}

func (x *ArticleDetailRep) GetLikes() []*LikeList {
	if x != nil {
		return x.Likes
	}
	return nil
}

type CommentListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	ArticleId     string                 `protobuf:"bytes,2,opt,name=article_id,json=articleId,proto3" json:"article_id,omitempty"`
	Page          uint32                 `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	Pagesize      uint32                 `protobuf:"varint,4,opt,name=pagesize,proto3" json:"pagesize,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CommentListReq) Reset() {
	*x = CommentListReq{}
	mi := &file_dapplink_chaineye_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommentListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommentListReq) ProtoMessage() {}

func (x *CommentListReq) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_chaineye_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommentListReq.ProtoReflect.Descriptor instead.
func (*CommentListReq) Descriptor() ([]byte, []int) {
	return file_dapplink_chaineye_proto_rawDescGZIP(), []int{11}
}

func (x *CommentListReq) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *CommentListReq) GetArticleId() string {
	if x != nil {
		return x.ArticleId
	}
	return ""
}

func (x *CommentListReq) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *CommentListReq) GetPagesize() uint32 {
	if x != nil {
		return x.Pagesize
	}
	return 0
}

type CommentListRep struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Comments      []*CommentList         `protobuf:"bytes,3,rep,name=comments,proto3" json:"comments,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CommentListRep) Reset() {
	*x = CommentListRep{}
	mi := &file_dapplink_chaineye_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommentListRep) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommentListRep) ProtoMessage() {}

func (x *CommentListRep) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_chaineye_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommentListRep.ProtoReflect.Descriptor instead.
func (*CommentListRep) Descriptor() ([]byte, []int) {
	return file_dapplink_chaineye_proto_rawDescGZIP(), []int{12}
}

func (x *CommentListRep) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *CommentListRep) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CommentListRep) GetComments() []*CommentList {
	if x != nil {
		return x.Comments
	}
	return nil
}

type AddressReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	AuthorId      string                 `protobuf:"bytes,2,opt,name=author_id,json=authorId,proto3" json:"author_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddressReq) Reset() {
	*x = AddressReq{}
	mi := &file_dapplink_chaineye_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddressReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddressReq) ProtoMessage() {}

func (x *AddressReq) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_chaineye_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddressReq.ProtoReflect.Descriptor instead.
func (*AddressReq) Descriptor() ([]byte, []int) {
	return file_dapplink_chaineye_proto_rawDescGZIP(), []int{13}
}

func (x *AddressReq) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *AddressReq) GetAuthorId() string {
	if x != nil {
		return x.AuthorId
	}
	return ""
}

type AddressRep struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	AssetAddress  []*AssetAddress        `protobuf:"bytes,3,rep,name=asset_address,json=assetAddress,proto3" json:"asset_address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddressRep) Reset() {
	*x = AddressRep{}
	mi := &file_dapplink_chaineye_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddressRep) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddressRep) ProtoMessage() {}

func (x *AddressRep) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_chaineye_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddressRep.ProtoReflect.Descriptor instead.
func (*AddressRep) Descriptor() ([]byte, []int) {
	return file_dapplink_chaineye_proto_rawDescGZIP(), []int{14}
}

func (x *AddressRep) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *AddressRep) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AddressRep) GetAssetAddress() []*AssetAddress {
	if x != nil {
		return x.AssetAddress
	}
	return nil
}

type LikeReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	TxHash        string                 `protobuf:"bytes,2,opt,name=tx_hash,json=txHash,proto3" json:"tx_hash,omitempty"`
	LikeFrom      string                 `protobuf:"bytes,3,opt,name=like_from,json=likeFrom,proto3" json:"like_from,omitempty"`
	LikeTo        string                 `protobuf:"bytes,4,opt,name=like_to,json=likeTo,proto3" json:"like_to,omitempty"`
	Amount        string                 `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`
	AssetName     string                 `protobuf:"bytes,6,opt,name=asset_name,json=assetName,proto3" json:"asset_name,omitempty"`
	TokenAddress  string                 `protobuf:"bytes,7,opt,name=token_address,json=tokenAddress,proto3" json:"token_address,omitempty"`
	AuthorId      string                 `protobuf:"bytes,8,opt,name=author_id,json=authorId,proto3" json:"author_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LikeReq) Reset() {
	*x = LikeReq{}
	mi := &file_dapplink_chaineye_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LikeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LikeReq) ProtoMessage() {}

func (x *LikeReq) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_chaineye_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LikeReq.ProtoReflect.Descriptor instead.
func (*LikeReq) Descriptor() ([]byte, []int) {
	return file_dapplink_chaineye_proto_rawDescGZIP(), []int{15}
}

func (x *LikeReq) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *LikeReq) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

func (x *LikeReq) GetLikeFrom() string {
	if x != nil {
		return x.LikeFrom
	}
	return ""
}

func (x *LikeReq) GetLikeTo() string {
	if x != nil {
		return x.LikeTo
	}
	return ""
}

func (x *LikeReq) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *LikeReq) GetAssetName() string {
	if x != nil {
		return x.AssetName
	}
	return ""
}

func (x *LikeReq) GetTokenAddress() string {
	if x != nil {
		return x.TokenAddress
	}
	return ""
}

func (x *LikeReq) GetAuthorId() string {
	if x != nil {
		return x.AuthorId
	}
	return ""
}

type LikeRep struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LikeRep) Reset() {
	*x = LikeRep{}
	mi := &file_dapplink_chaineye_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LikeRep) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LikeRep) ProtoMessage() {}

func (x *LikeRep) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_chaineye_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LikeRep.ProtoReflect.Descriptor instead.
func (*LikeRep) Descriptor() ([]byte, []int) {
	return file_dapplink_chaineye_proto_rawDescGZIP(), []int{16}
}

func (x *LikeRep) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *LikeRep) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

var File_dapplink_chaineye_proto protoreflect.FileDescriptor

var file_dapplink_chaineye_proto_rawDesc = string([]byte{
	0x0a, 0x17, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x65, 0x79, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x65, 0x79, 0x65, 0x1a, 0x15, 0x64, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xf1, 0x01, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x62, 0x73,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x62, 0x73,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x75, 0x74,
	0x68, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x75, 0x74, 0x68, 0x6f,
	0x72, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x69, 0x65, 0x77, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x76, 0x69, 0x65, 0x77, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x70, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x70, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f,
	0x76, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x69, 0x6b, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x6c, 0x69, 0x6b, 0x65, 0x22, 0xa8, 0x01, 0x0a, 0x0b, 0x43, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x69, 0x65, 0x77, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x76, 0x69, 0x65, 0x77, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x69, 0x6b, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x6c, 0x69, 0x6b, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x72, 0x65, 0x70, 0x6c, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x5c, 0x0a, 0x08, 0x4c, 0x69, 0x6b, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x73, 0x73, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x61, 0x73, 0x73, 0x65, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0x2d, 0x0a, 0x07, 0x43, 0x61, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22,
	0x3e, 0x0a, 0x0c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x61, 0x73, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22,
	0x4a, 0x0a, 0x0d, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x52, 0x65, 0x71,
	0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x82, 0x01, 0x0a, 0x0d,
	0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x52, 0x65, 0x70, 0x12, 0x28, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64,
	0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x35, 0x0a, 0x08, 0x63, 0x61, 0x74,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x64, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x65, 0x79, 0x65, 0x2e,
	0x43, 0x61, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x07, 0x63, 0x61, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x22, 0x92, 0x01, 0x0a, 0x0e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x15,
	0x0a, 0x06, 0x63, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x63, 0x61, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x9e, 0x01, 0x0a, 0x0e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x3a, 0x0a, 0x08, 0x61, 0x72,
	0x74, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x64,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x65, 0x79, 0x65,
	0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x08, 0x61, 0x72,
	0x74, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x22, 0x5d, 0x0a, 0x10, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x80, 0x03, 0x0a, 0x10, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x69, 0x65,
	0x77, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x69, 0x65, 0x77, 0x73, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x70,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x70,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x69, 0x6b, 0x65, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x6c, 0x69, 0x6b, 0x65, 0x12, 0x3a, 0x0a, 0x08, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x64, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x65, 0x79, 0x65, 0x2e,
	0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x08, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x31, 0x0a, 0x05, 0x6c, 0x69, 0x6b, 0x65, 0x73, 0x18, 0x0c,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x65, 0x79, 0x65, 0x2e, 0x4c, 0x69, 0x6b, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x05, 0x6c, 0x69, 0x6b, 0x65, 0x73, 0x22, 0x86, 0x01, 0x0a, 0x0e, 0x43, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x25, 0x0a, 0x0e, 0x63,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x73, 0x69, 0x7a,
	0x65, 0x22, 0x88, 0x01, 0x0a, 0x0e, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x70, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65,
	0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67,
	0x12, 0x3a, 0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x63, 0x68,
	0x61, 0x69, 0x6e, 0x65, 0x79, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x50, 0x0a, 0x0a,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x8e,
	0x01, 0x0a, 0x0a, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x70, 0x12, 0x28, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64,
	0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x44, 0x0a, 0x0d, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x65, 0x79, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x0c, 0x61, 0x73, 0x73, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22,
	0xf8, 0x01, 0x0a, 0x07, 0x4c, 0x69, 0x6b, 0x65, 0x52, 0x65, 0x71, 0x12, 0x25, 0x0a, 0x0e, 0x63,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x78, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x78, 0x48, 0x61, 0x73, 0x68, 0x12, 0x1b, 0x0a, 0x09, 0x6c,
	0x69, 0x6b, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6c, 0x69, 0x6b, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x17, 0x0a, 0x07, 0x6c, 0x69, 0x6b, 0x65,
	0x5f, 0x74, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x69, 0x6b, 0x65, 0x54,
	0x6f, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1b, 0x0a,
	0x09, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x45, 0x0a, 0x07, 0x4c, 0x69,
	0x6b, 0x65, 0x52, 0x65, 0x70, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52,
	0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x32, 0x97, 0x04, 0x0a, 0x0f, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x65, 0x79, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x55, 0x0a, 0x0d, 0x67, 0x65, 0x74, 0x41, 0x72, 0x74, 0x69,
	0x63, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x12, 0x20, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x65, 0x79, 0x65, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63,
	0x6c, 0x65, 0x43, 0x61, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x65, 0x79, 0x65, 0x2e, 0x41, 0x72, 0x74,
	0x69, 0x63, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x52, 0x65, 0x70, 0x22, 0x00, 0x12, 0x58, 0x0a, 0x0e,
	0x67, 0x65, 0x74, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x21,
	0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x65,
	0x79, 0x65, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x1a, 0x21, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x65, 0x79, 0x65, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x70, 0x22, 0x00, 0x12, 0x5e, 0x0a, 0x10, 0x67, 0x65, 0x74, 0x41, 0x72, 0x74,
	0x69, 0x63, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x23, 0x2e, 0x64, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x65, 0x79, 0x65, 0x2e, 0x41,
	0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x1a,
	0x23, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x65, 0x79, 0x65, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x70, 0x22, 0x00, 0x12, 0x58, 0x0a, 0x0e, 0x67, 0x65, 0x74, 0x43, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x21, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x65, 0x79, 0x65, 0x2e, 0x43, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x64, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x65, 0x79, 0x65, 0x2e,
	0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x22, 0x00,
	0x12, 0x50, 0x0a, 0x0e, 0x67, 0x65, 0x74, 0x4c, 0x69, 0x6b, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x1d, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x63, 0x68,
	0x61, 0x69, 0x6e, 0x65, 0x79, 0x65, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65,
	0x71, 0x1a, 0x1d, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x65, 0x79, 0x65, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x70,
	0x22, 0x00, 0x12, 0x47, 0x0a, 0x0b, 0x6c, 0x69, 0x6b, 0x65, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x12, 0x1a, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x65, 0x79, 0x65, 0x2e, 0x4c, 0x69, 0x6b, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e,
	0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x65, 0x79,
	0x65, 0x2e, 0x4c, 0x69, 0x6b, 0x65, 0x52, 0x65, 0x70, 0x22, 0x00, 0x42, 0x29, 0x0a, 0x15, 0x78,
	0x79, 0x7a, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x65, 0x79, 0x65, 0x5a, 0x10, 0x2e, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x68,
	0x61, 0x69, 0x6e, 0x65, 0x79, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_dapplink_chaineye_proto_rawDescOnce sync.Once
	file_dapplink_chaineye_proto_rawDescData []byte
)

func file_dapplink_chaineye_proto_rawDescGZIP() []byte {
	file_dapplink_chaineye_proto_rawDescOnce.Do(func() {
		file_dapplink_chaineye_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_dapplink_chaineye_proto_rawDesc), len(file_dapplink_chaineye_proto_rawDesc)))
	})
	return file_dapplink_chaineye_proto_rawDescData
}

var file_dapplink_chaineye_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_dapplink_chaineye_proto_goTypes = []any{
	(*ArticleList)(nil),      // 0: dapplink.chaineye.ArticleList
	(*CommentList)(nil),      // 1: dapplink.chaineye.CommentList
	(*LikeList)(nil),         // 2: dapplink.chaineye.LikeList
	(*CatList)(nil),          // 3: dapplink.chaineye.CatList
	(*AssetAddress)(nil),     // 4: dapplink.chaineye.AssetAddress
	(*ArticleCatReq)(nil),    // 5: dapplink.chaineye.ArticleCatReq
	(*ArticleCatRep)(nil),    // 6: dapplink.chaineye.ArticleCatRep
	(*ArticleListReq)(nil),   // 7: dapplink.chaineye.ArticleListReq
	(*ArticleListRep)(nil),   // 8: dapplink.chaineye.ArticleListRep
	(*ArticleDetailReq)(nil), // 9: dapplink.chaineye.ArticleDetailReq
	(*ArticleDetailRep)(nil), // 10: dapplink.chaineye.ArticleDetailRep
	(*CommentListReq)(nil),   // 11: dapplink.chaineye.CommentListReq
	(*CommentListRep)(nil),   // 12: dapplink.chaineye.CommentListRep
	(*AddressReq)(nil),       // 13: dapplink.chaineye.AddressReq
	(*AddressRep)(nil),       // 14: dapplink.chaineye.AddressRep
	(*LikeReq)(nil),          // 15: dapplink.chaineye.LikeReq
	(*LikeRep)(nil),          // 16: dapplink.chaineye.LikeRep
	(common.ReturnCode)(0),   // 17: dapplink.ReturnCode
}
var file_dapplink_chaineye_proto_depIdxs = []int32{
	17, // 0: dapplink.chaineye.ArticleCatRep.code:type_name -> dapplink.ReturnCode
	3,  // 1: dapplink.chaineye.ArticleCatRep.cat_list:type_name -> dapplink.chaineye.CatList
	17, // 2: dapplink.chaineye.ArticleListRep.code:type_name -> dapplink.ReturnCode
	0,  // 3: dapplink.chaineye.ArticleListRep.articles:type_name -> dapplink.chaineye.ArticleList
	17, // 4: dapplink.chaineye.ArticleDetailRep.code:type_name -> dapplink.ReturnCode
	1,  // 5: dapplink.chaineye.ArticleDetailRep.comments:type_name -> dapplink.chaineye.CommentList
	2,  // 6: dapplink.chaineye.ArticleDetailRep.likes:type_name -> dapplink.chaineye.LikeList
	17, // 7: dapplink.chaineye.CommentListRep.code:type_name -> dapplink.ReturnCode
	1,  // 8: dapplink.chaineye.CommentListRep.comments:type_name -> dapplink.chaineye.CommentList
	17, // 9: dapplink.chaineye.AddressRep.code:type_name -> dapplink.ReturnCode
	4,  // 10: dapplink.chaineye.AddressRep.asset_address:type_name -> dapplink.chaineye.AssetAddress
	17, // 11: dapplink.chaineye.LikeRep.code:type_name -> dapplink.ReturnCode
	5,  // 12: dapplink.chaineye.ChaineyeService.getArticleCat:input_type -> dapplink.chaineye.ArticleCatReq
	7,  // 13: dapplink.chaineye.ChaineyeService.getArticleList:input_type -> dapplink.chaineye.ArticleListReq
	9,  // 14: dapplink.chaineye.ChaineyeService.getArticleDetail:input_type -> dapplink.chaineye.ArticleDetailReq
	11, // 15: dapplink.chaineye.ChaineyeService.getCommentList:input_type -> dapplink.chaineye.CommentListReq
	13, // 16: dapplink.chaineye.ChaineyeService.getLikeAddress:input_type -> dapplink.chaineye.AddressReq
	15, // 17: dapplink.chaineye.ChaineyeService.likeArticle:input_type -> dapplink.chaineye.LikeReq
	6,  // 18: dapplink.chaineye.ChaineyeService.getArticleCat:output_type -> dapplink.chaineye.ArticleCatRep
	8,  // 19: dapplink.chaineye.ChaineyeService.getArticleList:output_type -> dapplink.chaineye.ArticleListRep
	10, // 20: dapplink.chaineye.ChaineyeService.getArticleDetail:output_type -> dapplink.chaineye.ArticleDetailRep
	12, // 21: dapplink.chaineye.ChaineyeService.getCommentList:output_type -> dapplink.chaineye.CommentListRep
	14, // 22: dapplink.chaineye.ChaineyeService.getLikeAddress:output_type -> dapplink.chaineye.AddressRep
	16, // 23: dapplink.chaineye.ChaineyeService.likeArticle:output_type -> dapplink.chaineye.LikeRep
	18, // [18:24] is the sub-list for method output_type
	12, // [12:18] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_dapplink_chaineye_proto_init() }
func file_dapplink_chaineye_proto_init() {
	if File_dapplink_chaineye_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_dapplink_chaineye_proto_rawDesc), len(file_dapplink_chaineye_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_dapplink_chaineye_proto_goTypes,
		DependencyIndexes: file_dapplink_chaineye_proto_depIdxs,
		MessageInfos:      file_dapplink_chaineye_proto_msgTypes,
	}.Build()
	File_dapplink_chaineye_proto = out.File
	file_dapplink_chaineye_proto_goTypes = nil
	file_dapplink_chaineye_proto_depIdxs = nil
}
