// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v4.25.3
// source: dapplink/airdrop.proto

package airdrop

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DppLinkPointsReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Type          string                 `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Address       string                 `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DppLinkPointsReq) Reset() {
	*x = DppLinkPointsReq{}
	mi := &file_dapplink_airdrop_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DppLinkPointsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DppLinkPointsReq) ProtoMessage() {}

func (x *DppLinkPointsReq) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_airdrop_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DppLinkPointsReq.ProtoReflect.Descriptor instead.
func (*DppLinkPointsReq) Descriptor() ([]byte, []int) {
	return file_dapplink_airdrop_proto_rawDescGZIP(), []int{0}
}

func (x *DppLinkPointsReq) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *DppLinkPointsReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *DppLinkPointsReq) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type DppLinkPointsRep struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          string                 `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DppLinkPointsRep) Reset() {
	*x = DppLinkPointsRep{}
	mi := &file_dapplink_airdrop_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DppLinkPointsRep) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DppLinkPointsRep) ProtoMessage() {}

func (x *DppLinkPointsRep) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_airdrop_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DppLinkPointsRep.ProtoReflect.Descriptor instead.
func (*DppLinkPointsRep) Descriptor() ([]byte, []int) {
	return file_dapplink_airdrop_proto_rawDescGZIP(), []int{1}
}

func (x *DppLinkPointsRep) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *DppLinkPointsRep) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

var File_dapplink_airdrop_proto protoreflect.FileDescriptor

var file_dapplink_airdrop_proto_rawDesc = string([]byte{
	0x0a, 0x16, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x61, 0x69, 0x72, 0x64, 0x72,
	0x6f, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x69, 0x72, 0x64,
	0x72, 0x6f, 0x70, 0x22, 0x67, 0x0a, 0x10, 0x44, 0x70, 0x70, 0x4c, 0x69, 0x6e, 0x6b, 0x50, 0x6f,
	0x69, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x38, 0x0a, 0x10,
	0x44, 0x70, 0x70, 0x4c, 0x69, 0x6e, 0x6b, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x70,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x32, 0x83, 0x01, 0x0a, 0x0e, 0x41, 0x69, 0x72, 0x64, 0x72,
	0x6f, 0x70, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x71, 0x0a, 0x13, 0x73, 0x75, 0x62,
	0x6d, 0x69, 0x74, 0x44, 0x70, 0x70, 0x4c, 0x69, 0x6e, 0x6b, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73,
	0x12, 0x2b, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x64, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x69, 0x72, 0x64, 0x72, 0x6f, 0x70, 0x2e, 0x44, 0x70, 0x70,
	0x4c, 0x69, 0x6e, 0x6b, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x2b, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x61, 0x69, 0x72, 0x64, 0x72, 0x6f, 0x70, 0x2e, 0x44, 0x70, 0x70, 0x4c, 0x69, 0x6e,
	0x6b, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x70, 0x22, 0x00, 0x42, 0x27, 0x0a, 0x14,
	0x78, 0x79, 0x7a, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x69, 0x72,
	0x64, 0x72, 0x6f, 0x70, 0x5a, 0x0f, 0x2e, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x69,
	0x72, 0x64, 0x72, 0x6f, 0x70, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_dapplink_airdrop_proto_rawDescOnce sync.Once
	file_dapplink_airdrop_proto_rawDescData []byte
)

func file_dapplink_airdrop_proto_rawDescGZIP() []byte {
	file_dapplink_airdrop_proto_rawDescOnce.Do(func() {
		file_dapplink_airdrop_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_dapplink_airdrop_proto_rawDesc), len(file_dapplink_airdrop_proto_rawDesc)))
	})
	return file_dapplink_airdrop_proto_rawDescData
}

var file_dapplink_airdrop_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_dapplink_airdrop_proto_goTypes = []any{
	(*DppLinkPointsReq)(nil), // 0: services.dapplink.airdrop.DppLinkPointsReq
	(*DppLinkPointsRep)(nil), // 1: services.dapplink.airdrop.DppLinkPointsRep
}
var file_dapplink_airdrop_proto_depIdxs = []int32{
	0, // 0: services.dapplink.airdrop.AirdropService.submitDppLinkPoints:input_type -> services.dapplink.airdrop.DppLinkPointsReq
	1, // 1: services.dapplink.airdrop.AirdropService.submitDppLinkPoints:output_type -> services.dapplink.airdrop.DppLinkPointsRep
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_dapplink_airdrop_proto_init() }
func file_dapplink_airdrop_proto_init() {
	if File_dapplink_airdrop_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_dapplink_airdrop_proto_rawDesc), len(file_dapplink_airdrop_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_dapplink_airdrop_proto_goTypes,
		DependencyIndexes: file_dapplink_airdrop_proto_depIdxs,
		MessageInfos:      file_dapplink_airdrop_proto_msgTypes,
	}.Build()
	File_dapplink_airdrop_proto = out.File
	file_dapplink_airdrop_proto_goTypes = nil
	file_dapplink_airdrop_proto_depIdxs = nil
}
