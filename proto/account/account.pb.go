// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v4.25.3
// source: dapplink/account.proto

package account

import (
	common "./proto/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TxStatus int32

const (
	TxStatus_NotFound              TxStatus = 0
	TxStatus_Pending               TxStatus = 1
	TxStatus_Failed                TxStatus = 2
	TxStatus_Success               TxStatus = 3
	TxStatus_ContractExecuteFailed TxStatus = 4
	TxStatus_Other                 TxStatus = 5
)

// Enum value maps for TxStatus.
var (
	TxStatus_name = map[int32]string{
		0: "NotFound",
		1: "Pending",
		2: "Failed",
		3: "Success",
		4: "ContractExecuteFailed",
		5: "Other",
	}
	TxStatus_value = map[string]int32{
		"NotFound":              0,
		"Pending":               1,
		"Failed":                2,
		"Success":               3,
		"ContractExecuteFailed": 4,
		"Other":                 5,
	}
)

func (x TxStatus) Enum() *TxStatus {
	p := new(TxStatus)
	*p = x
	return p
}

func (x TxStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TxStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_dapplink_account_proto_enumTypes[0].Descriptor()
}

func (TxStatus) Type() protoreflect.EnumType {
	return &file_dapplink_account_proto_enumTypes[0]
}

func (x TxStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TxStatus.Descriptor instead.
func (TxStatus) EnumDescriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{0}
}

type TxMessage struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Hash            string                 `protobuf:"bytes,1,opt,name=hash,proto3" json:"hash,omitempty"`
	Index           uint32                 `protobuf:"varint,2,opt,name=index,proto3" json:"index,omitempty"`
	From            string                 `protobuf:"bytes,3,opt,name=from,proto3" json:"from,omitempty"`
	To              string                 `protobuf:"bytes,4,opt,name=to,proto3" json:"to,omitempty"`
	Value           string                 `protobuf:"bytes,7,opt,name=value,proto3" json:"value,omitempty"`
	Fee             string                 `protobuf:"bytes,5,opt,name=fee,proto3" json:"fee,omitempty"`
	Status          TxStatus               `protobuf:"varint,6,opt,name=status,proto3,enum=dapplink.account.TxStatus" json:"status,omitempty"`
	Type            int32                  `protobuf:"varint,8,opt,name=type,proto3" json:"type,omitempty"`
	Height          string                 `protobuf:"bytes,9,opt,name=height,proto3" json:"height,omitempty"`
	ContractAddress string                 `protobuf:"bytes,10,opt,name=contract_address,json=contractAddress,proto3" json:"contract_address,omitempty"`
	Datetime        string                 `protobuf:"bytes,11,opt,name=datetime,proto3" json:"datetime,omitempty"`
	Data            string                 `protobuf:"bytes,12,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *TxMessage) Reset() {
	*x = TxMessage{}
	mi := &file_dapplink_account_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TxMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxMessage) ProtoMessage() {}

func (x *TxMessage) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxMessage.ProtoReflect.Descriptor instead.
func (*TxMessage) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{0}
}

func (x *TxMessage) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *TxMessage) GetIndex() uint32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *TxMessage) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *TxMessage) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

func (x *TxMessage) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *TxMessage) GetFee() string {
	if x != nil {
		return x.Fee
	}
	return ""
}

func (x *TxMessage) GetStatus() TxStatus {
	if x != nil {
		return x.Status
	}
	return TxStatus_NotFound
}

func (x *TxMessage) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *TxMessage) GetHeight() string {
	if x != nil {
		return x.Height
	}
	return ""
}

func (x *TxMessage) GetContractAddress() string {
	if x != nil {
		return x.ContractAddress
	}
	return ""
}

func (x *TxMessage) GetDatetime() string {
	if x != nil {
		return x.Datetime
	}
	return ""
}

func (x *TxMessage) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type BlockData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Hash          string                 `protobuf:"bytes,1,opt,name=hash,proto3" json:"hash,omitempty"`
	BaseFee       string                 `protobuf:"bytes,2,opt,name=baseFee,proto3" json:"baseFee,omitempty"`
	Transactions  []*TxMessage           `protobuf:"bytes,3,rep,name=transactions,proto3" json:"transactions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockData) Reset() {
	*x = BlockData{}
	mi := &file_dapplink_account_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockData) ProtoMessage() {}

func (x *BlockData) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockData.ProtoReflect.Descriptor instead.
func (*BlockData) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{1}
}

func (x *BlockData) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *BlockData) GetBaseFee() string {
	if x != nil {
		return x.BaseFee
	}
	return ""
}

func (x *BlockData) GetTransactions() []*TxMessage {
	if x != nil {
		return x.Transactions
	}
	return nil
}

type BlockHeader struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Hash             string                 `protobuf:"bytes,1,opt,name=hash,proto3" json:"hash,omitempty"`
	ParentHash       string                 `protobuf:"bytes,2,opt,name=parent_hash,json=parentHash,proto3" json:"parent_hash,omitempty"`
	UncleHash        string                 `protobuf:"bytes,3,opt,name=uncle_hash,json=uncleHash,proto3" json:"uncle_hash,omitempty"`
	CoinBase         string                 `protobuf:"bytes,4,opt,name=coin_base,json=coinBase,proto3" json:"coin_base,omitempty"`
	Root             string                 `protobuf:"bytes,5,opt,name=root,proto3" json:"root,omitempty"`
	TxHash           string                 `protobuf:"bytes,6,opt,name=tx_hash,json=txHash,proto3" json:"tx_hash,omitempty"`
	ReceiptHash      string                 `protobuf:"bytes,7,opt,name=receipt_hash,json=receiptHash,proto3" json:"receipt_hash,omitempty"`
	ParentBeaconRoot string                 `protobuf:"bytes,8,opt,name=parent_beacon_root,json=parentBeaconRoot,proto3" json:"parent_beacon_root,omitempty"`
	Difficulty       string                 `protobuf:"bytes,9,opt,name=difficulty,proto3" json:"difficulty,omitempty"`
	Number           string                 `protobuf:"bytes,10,opt,name=number,proto3" json:"number,omitempty"`
	GasLimit         uint64                 `protobuf:"varint,11,opt,name=gas_limit,json=gasLimit,proto3" json:"gas_limit,omitempty"`
	GasUsed          uint64                 `protobuf:"varint,12,opt,name=gas_used,json=gasUsed,proto3" json:"gas_used,omitempty"`
	Time             uint64                 `protobuf:"varint,13,opt,name=time,proto3" json:"time,omitempty"`
	Extra            string                 `protobuf:"bytes,14,opt,name=extra,proto3" json:"extra,omitempty"`
	MixDigest        string                 `protobuf:"bytes,15,opt,name=mix_digest,json=mixDigest,proto3" json:"mix_digest,omitempty"`
	Nonce            string                 `protobuf:"bytes,16,opt,name=nonce,proto3" json:"nonce,omitempty"`
	BaseFee          string                 `protobuf:"bytes,17,opt,name=base_fee,json=baseFee,proto3" json:"base_fee,omitempty"`
	WithdrawalsHash  string                 `protobuf:"bytes,18,opt,name=withdrawals_hash,json=withdrawalsHash,proto3" json:"withdrawals_hash,omitempty"`
	BlobGasUsed      uint64                 `protobuf:"varint,19,opt,name=blob_gas_used,json=blobGasUsed,proto3" json:"blob_gas_used,omitempty"`
	ExcessBlobGas    uint64                 `protobuf:"varint,20,opt,name=excess_blob_gas,json=excessBlobGas,proto3" json:"excess_blob_gas,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *BlockHeader) Reset() {
	*x = BlockHeader{}
	mi := &file_dapplink_account_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockHeader) ProtoMessage() {}

func (x *BlockHeader) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockHeader.ProtoReflect.Descriptor instead.
func (*BlockHeader) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{2}
}

func (x *BlockHeader) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *BlockHeader) GetParentHash() string {
	if x != nil {
		return x.ParentHash
	}
	return ""
}

func (x *BlockHeader) GetUncleHash() string {
	if x != nil {
		return x.UncleHash
	}
	return ""
}

func (x *BlockHeader) GetCoinBase() string {
	if x != nil {
		return x.CoinBase
	}
	return ""
}

func (x *BlockHeader) GetRoot() string {
	if x != nil {
		return x.Root
	}
	return ""
}

func (x *BlockHeader) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

func (x *BlockHeader) GetReceiptHash() string {
	if x != nil {
		return x.ReceiptHash
	}
	return ""
}

func (x *BlockHeader) GetParentBeaconRoot() string {
	if x != nil {
		return x.ParentBeaconRoot
	}
	return ""
}

func (x *BlockHeader) GetDifficulty() string {
	if x != nil {
		return x.Difficulty
	}
	return ""
}

func (x *BlockHeader) GetNumber() string {
	if x != nil {
		return x.Number
	}
	return ""
}

func (x *BlockHeader) GetGasLimit() uint64 {
	if x != nil {
		return x.GasLimit
	}
	return 0
}

func (x *BlockHeader) GetGasUsed() uint64 {
	if x != nil {
		return x.GasUsed
	}
	return 0
}

func (x *BlockHeader) GetTime() uint64 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *BlockHeader) GetExtra() string {
	if x != nil {
		return x.Extra
	}
	return ""
}

func (x *BlockHeader) GetMixDigest() string {
	if x != nil {
		return x.MixDigest
	}
	return ""
}

func (x *BlockHeader) GetNonce() string {
	if x != nil {
		return x.Nonce
	}
	return ""
}

func (x *BlockHeader) GetBaseFee() string {
	if x != nil {
		return x.BaseFee
	}
	return ""
}

func (x *BlockHeader) GetWithdrawalsHash() string {
	if x != nil {
		return x.WithdrawalsHash
	}
	return ""
}

func (x *BlockHeader) GetBlobGasUsed() uint64 {
	if x != nil {
		return x.BlobGasUsed
	}
	return 0
}

func (x *BlockHeader) GetExcessBlobGas() uint64 {
	if x != nil {
		return x.ExcessBlobGas
	}
	return 0
}

type Log struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Address       string                 `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	Topics        []string               `protobuf:"bytes,2,rep,name=topics,proto3" json:"topics,omitempty"`
	Data          string                 `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	BlockNumber   string                 `protobuf:"bytes,4,opt,name=block_number,json=blockNumber,proto3" json:"block_number,omitempty"`
	TxHash        string                 `protobuf:"bytes,5,opt,name=tx_hash,json=txHash,proto3" json:"tx_hash,omitempty"`
	TxIndex       uint64                 `protobuf:"varint,6,opt,name=tx_index,json=txIndex,proto3" json:"tx_index,omitempty"`
	BlockHash     string                 `protobuf:"bytes,7,opt,name=block_hash,json=blockHash,proto3" json:"block_hash,omitempty"`
	Index         uint64                 `protobuf:"varint,8,opt,name=index,proto3" json:"index,omitempty"`
	Removed       bool                   `protobuf:"varint,9,opt,name=removed,proto3" json:"removed,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Log) Reset() {
	*x = Log{}
	mi := &file_dapplink_account_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Log) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Log) ProtoMessage() {}

func (x *Log) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Log.ProtoReflect.Descriptor instead.
func (*Log) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{3}
}

func (x *Log) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *Log) GetTopics() []string {
	if x != nil {
		return x.Topics
	}
	return nil
}

func (x *Log) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *Log) GetBlockNumber() string {
	if x != nil {
		return x.BlockNumber
	}
	return ""
}

func (x *Log) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

func (x *Log) GetTxIndex() uint64 {
	if x != nil {
		return x.TxIndex
	}
	return 0
}

func (x *Log) GetBlockHash() string {
	if x != nil {
		return x.BlockHash
	}
	return ""
}

func (x *Log) GetIndex() uint64 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *Log) GetRemoved() bool {
	if x != nil {
		return x.Removed
	}
	return false
}

type SupportChainsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SupportChainsRequest) Reset() {
	*x = SupportChainsRequest{}
	mi := &file_dapplink_account_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SupportChainsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SupportChainsRequest) ProtoMessage() {}

func (x *SupportChainsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SupportChainsRequest.ProtoReflect.Descriptor instead.
func (*SupportChainsRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{4}
}

func (x *SupportChainsRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *SupportChainsRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *SupportChainsRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

type SupportChainsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Support       bool                   `protobuf:"varint,3,opt,name=support,proto3" json:"support,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SupportChainsResponse) Reset() {
	*x = SupportChainsResponse{}
	mi := &file_dapplink_account_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SupportChainsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SupportChainsResponse) ProtoMessage() {}

func (x *SupportChainsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SupportChainsResponse.ProtoReflect.Descriptor instead.
func (*SupportChainsResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{5}
}

func (x *SupportChainsResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *SupportChainsResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SupportChainsResponse) GetSupport() bool {
	if x != nil {
		return x.Support
	}
	return false
}

type ConvertAddressRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
	Type          string                 `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	PublicKey     string                 `protobuf:"bytes,5,opt,name=public_key,json=publicKey,proto3" json:"public_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConvertAddressRequest) Reset() {
	*x = ConvertAddressRequest{}
	mi := &file_dapplink_account_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConvertAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConvertAddressRequest) ProtoMessage() {}

func (x *ConvertAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConvertAddressRequest.ProtoReflect.Descriptor instead.
func (*ConvertAddressRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{6}
}

func (x *ConvertAddressRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *ConvertAddressRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *ConvertAddressRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *ConvertAddressRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ConvertAddressRequest) GetPublicKey() string {
	if x != nil {
		return x.PublicKey
	}
	return ""
}

type ConvertAddressResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Address       string                 `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConvertAddressResponse) Reset() {
	*x = ConvertAddressResponse{}
	mi := &file_dapplink_account_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConvertAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConvertAddressResponse) ProtoMessage() {}

func (x *ConvertAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConvertAddressResponse.ProtoReflect.Descriptor instead.
func (*ConvertAddressResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{7}
}

func (x *ConvertAddressResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *ConvertAddressResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ConvertAddressResponse) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type ValidAddressRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
	Address       string                 `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidAddressRequest) Reset() {
	*x = ValidAddressRequest{}
	mi := &file_dapplink_account_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidAddressRequest) ProtoMessage() {}

func (x *ValidAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidAddressRequest.ProtoReflect.Descriptor instead.
func (*ValidAddressRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{8}
}

func (x *ValidAddressRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *ValidAddressRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *ValidAddressRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *ValidAddressRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type ValidAddressResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Valid         bool                   `protobuf:"varint,3,opt,name=valid,proto3" json:"valid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidAddressResponse) Reset() {
	*x = ValidAddressResponse{}
	mi := &file_dapplink_account_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidAddressResponse) ProtoMessage() {}

func (x *ValidAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidAddressResponse.ProtoReflect.Descriptor instead.
func (*ValidAddressResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{9}
}

func (x *ValidAddressResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *ValidAddressResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ValidAddressResponse) GetValid() bool {
	if x != nil {
		return x.Valid
	}
	return false
}

type BlockNumberRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Height        int64                  `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
	ViewTx        bool                   `protobuf:"varint,4,opt,name=view_tx,json=viewTx,proto3" json:"view_tx,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockNumberRequest) Reset() {
	*x = BlockNumberRequest{}
	mi := &file_dapplink_account_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockNumberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockNumberRequest) ProtoMessage() {}

func (x *BlockNumberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockNumberRequest.ProtoReflect.Descriptor instead.
func (*BlockNumberRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{10}
}

func (x *BlockNumberRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *BlockNumberRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *BlockNumberRequest) GetHeight() int64 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *BlockNumberRequest) GetViewTx() bool {
	if x != nil {
		return x.ViewTx
	}
	return false
}

type BlockHashRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Hash          string                 `protobuf:"bytes,3,opt,name=hash,proto3" json:"hash,omitempty"`
	ViewTx        bool                   `protobuf:"varint,4,opt,name=view_tx,json=viewTx,proto3" json:"view_tx,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockHashRequest) Reset() {
	*x = BlockHashRequest{}
	mi := &file_dapplink_account_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockHashRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockHashRequest) ProtoMessage() {}

func (x *BlockHashRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockHashRequest.ProtoReflect.Descriptor instead.
func (*BlockHashRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{11}
}

func (x *BlockHashRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *BlockHashRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *BlockHashRequest) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *BlockHashRequest) GetViewTx() bool {
	if x != nil {
		return x.ViewTx
	}
	return false
}

type BlockInfoTransactionList struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	From           string                 `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"`
	To             string                 `protobuf:"bytes,2,opt,name=to,proto3" json:"to,omitempty"`
	TokenAddress   string                 `protobuf:"bytes,3,opt,name=token_address,json=tokenAddress,proto3" json:"token_address,omitempty"`
	ContractWallet string                 `protobuf:"bytes,4,opt,name=contract_wallet,json=contractWallet,proto3" json:"contract_wallet,omitempty"`
	Hash           string                 `protobuf:"bytes,5,opt,name=hash,proto3" json:"hash,omitempty"`
	Height         uint64                 `protobuf:"varint,6,opt,name=height,proto3" json:"height,omitempty"`
	Amount         string                 `protobuf:"bytes,7,opt,name=amount,proto3" json:"amount,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BlockInfoTransactionList) Reset() {
	*x = BlockInfoTransactionList{}
	mi := &file_dapplink_account_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockInfoTransactionList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockInfoTransactionList) ProtoMessage() {}

func (x *BlockInfoTransactionList) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockInfoTransactionList.ProtoReflect.Descriptor instead.
func (*BlockInfoTransactionList) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{12}
}

func (x *BlockInfoTransactionList) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *BlockInfoTransactionList) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

func (x *BlockInfoTransactionList) GetTokenAddress() string {
	if x != nil {
		return x.TokenAddress
	}
	return ""
}

func (x *BlockInfoTransactionList) GetContractWallet() string {
	if x != nil {
		return x.ContractWallet
	}
	return ""
}

func (x *BlockInfoTransactionList) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *BlockInfoTransactionList) GetHeight() uint64 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *BlockInfoTransactionList) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

type BlockResponse struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Code          common.ReturnCode           `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                      `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Height        int64                       `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
	Hash          string                      `protobuf:"bytes,4,opt,name=hash,proto3" json:"hash,omitempty"`
	BaseFee       string                      `protobuf:"bytes,5,opt,name=base_fee,json=baseFee,proto3" json:"base_fee,omitempty"`
	Transactions  []*BlockInfoTransactionList `protobuf:"bytes,6,rep,name=transactions,proto3" json:"transactions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockResponse) Reset() {
	*x = BlockResponse{}
	mi := &file_dapplink_account_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockResponse) ProtoMessage() {}

func (x *BlockResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockResponse.ProtoReflect.Descriptor instead.
func (*BlockResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{13}
}

func (x *BlockResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *BlockResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *BlockResponse) GetHeight() int64 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *BlockResponse) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *BlockResponse) GetBaseFee() string {
	if x != nil {
		return x.BaseFee
	}
	return ""
}

func (x *BlockResponse) GetTransactions() []*BlockInfoTransactionList {
	if x != nil {
		return x.Transactions
	}
	return nil
}

type BlockHeaderHashRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
	Hash          string                 `protobuf:"bytes,4,opt,name=hash,proto3" json:"hash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockHeaderHashRequest) Reset() {
	*x = BlockHeaderHashRequest{}
	mi := &file_dapplink_account_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockHeaderHashRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockHeaderHashRequest) ProtoMessage() {}

func (x *BlockHeaderHashRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockHeaderHashRequest.ProtoReflect.Descriptor instead.
func (*BlockHeaderHashRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{14}
}

func (x *BlockHeaderHashRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *BlockHeaderHashRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *BlockHeaderHashRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *BlockHeaderHashRequest) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

type BlockHeaderNumberRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
	Height        int64                  `protobuf:"varint,4,opt,name=height,proto3" json:"height,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockHeaderNumberRequest) Reset() {
	*x = BlockHeaderNumberRequest{}
	mi := &file_dapplink_account_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockHeaderNumberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockHeaderNumberRequest) ProtoMessage() {}

func (x *BlockHeaderNumberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockHeaderNumberRequest.ProtoReflect.Descriptor instead.
func (*BlockHeaderNumberRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{15}
}

func (x *BlockHeaderNumberRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *BlockHeaderNumberRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *BlockHeaderNumberRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *BlockHeaderNumberRequest) GetHeight() int64 {
	if x != nil {
		return x.Height
	}
	return 0
}

type BlockHeaderResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	BlockHeader   *BlockHeader           `protobuf:"bytes,3,opt,name=block_header,json=blockHeader,proto3" json:"block_header,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockHeaderResponse) Reset() {
	*x = BlockHeaderResponse{}
	mi := &file_dapplink_account_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockHeaderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockHeaderResponse) ProtoMessage() {}

func (x *BlockHeaderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockHeaderResponse.ProtoReflect.Descriptor instead.
func (*BlockHeaderResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{16}
}

func (x *BlockHeaderResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *BlockHeaderResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *BlockHeaderResponse) GetBlockHeader() *BlockHeader {
	if x != nil {
		return x.BlockHeader
	}
	return nil
}

type BlockByRangeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
	Start         string                 `protobuf:"bytes,4,opt,name=start,proto3" json:"start,omitempty"`
	End           string                 `protobuf:"bytes,5,opt,name=end,proto3" json:"end,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockByRangeRequest) Reset() {
	*x = BlockByRangeRequest{}
	mi := &file_dapplink_account_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockByRangeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockByRangeRequest) ProtoMessage() {}

func (x *BlockByRangeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockByRangeRequest.ProtoReflect.Descriptor instead.
func (*BlockByRangeRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{17}
}

func (x *BlockByRangeRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *BlockByRangeRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *BlockByRangeRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *BlockByRangeRequest) GetStart() string {
	if x != nil {
		return x.Start
	}
	return ""
}

func (x *BlockByRangeRequest) GetEnd() string {
	if x != nil {
		return x.End
	}
	return ""
}

type BlockByRangeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	BlockHeader   []*BlockHeader         `protobuf:"bytes,3,rep,name=block_header,json=blockHeader,proto3" json:"block_header,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockByRangeResponse) Reset() {
	*x = BlockByRangeResponse{}
	mi := &file_dapplink_account_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockByRangeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockByRangeResponse) ProtoMessage() {}

func (x *BlockByRangeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockByRangeResponse.ProtoReflect.Descriptor instead.
func (*BlockByRangeResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{18}
}

func (x *BlockByRangeResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *BlockByRangeResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *BlockByRangeResponse) GetBlockHeader() []*BlockHeader {
	if x != nil {
		return x.BlockHeader
	}
	return nil
}

type AccountRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken    string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain            string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Coin             string                 `protobuf:"bytes,3,opt,name=coin,proto3" json:"coin,omitempty"`
	Network          string                 `protobuf:"bytes,4,opt,name=network,proto3" json:"network,omitempty"`
	Address          string                 `protobuf:"bytes,5,opt,name=address,proto3" json:"address,omitempty"`
	ContractAddress  string                 `protobuf:"bytes,6,opt,name=contract_address,json=contractAddress,proto3" json:"contract_address,omitempty"`
	ProposerKeyIndex uint64                 `protobuf:"varint,7,opt,name=proposer_key_index,json=proposerKeyIndex,proto3" json:"proposer_key_index,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *AccountRequest) Reset() {
	*x = AccountRequest{}
	mi := &file_dapplink_account_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountRequest) ProtoMessage() {}

func (x *AccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountRequest.ProtoReflect.Descriptor instead.
func (*AccountRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{19}
}

func (x *AccountRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *AccountRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *AccountRequest) GetCoin() string {
	if x != nil {
		return x.Coin
	}
	return ""
}

func (x *AccountRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *AccountRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *AccountRequest) GetContractAddress() string {
	if x != nil {
		return x.ContractAddress
	}
	return ""
}

func (x *AccountRequest) GetProposerKeyIndex() uint64 {
	if x != nil {
		return x.ProposerKeyIndex
	}
	return 0
}

type AccountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Network       string                 `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
	AccountNumber string                 `protobuf:"bytes,4,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	Sequence      string                 `protobuf:"bytes,5,opt,name=sequence,proto3" json:"sequence,omitempty"`
	Balance       string                 `protobuf:"bytes,6,opt,name=balance,proto3" json:"balance,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountResponse) Reset() {
	*x = AccountResponse{}
	mi := &file_dapplink_account_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountResponse) ProtoMessage() {}

func (x *AccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountResponse.ProtoReflect.Descriptor instead.
func (*AccountResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{20}
}

func (x *AccountResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *AccountResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AccountResponse) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *AccountResponse) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *AccountResponse) GetSequence() string {
	if x != nil {
		return x.Sequence
	}
	return ""
}

func (x *AccountResponse) GetBalance() string {
	if x != nil {
		return x.Balance
	}
	return ""
}

type FeeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Coin          string                 `protobuf:"bytes,3,opt,name=coin,proto3" json:"coin,omitempty"`
	Network       string                 `protobuf:"bytes,4,opt,name=network,proto3" json:"network,omitempty"`
	RawTx         string                 `protobuf:"bytes,5,opt,name=rawTx,proto3" json:"rawTx,omitempty"`
	Address       string                 `protobuf:"bytes,6,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FeeRequest) Reset() {
	*x = FeeRequest{}
	mi := &file_dapplink_account_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FeeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeeRequest) ProtoMessage() {}

func (x *FeeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeeRequest.ProtoReflect.Descriptor instead.
func (*FeeRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{21}
}

func (x *FeeRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *FeeRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *FeeRequest) GetCoin() string {
	if x != nil {
		return x.Coin
	}
	return ""
}

func (x *FeeRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *FeeRequest) GetRawTx() string {
	if x != nil {
		return x.RawTx
	}
	return ""
}

func (x *FeeRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type FeeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	SlowFee       string                 `protobuf:"bytes,3,opt,name=slow_fee,json=slowFee,proto3" json:"slow_fee,omitempty"`
	NormalFee     string                 `protobuf:"bytes,4,opt,name=normal_fee,json=normalFee,proto3" json:"normal_fee,omitempty"`
	FastFee       string                 `protobuf:"bytes,5,opt,name=fast_fee,json=fastFee,proto3" json:"fast_fee,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FeeResponse) Reset() {
	*x = FeeResponse{}
	mi := &file_dapplink_account_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FeeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeeResponse) ProtoMessage() {}

func (x *FeeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeeResponse.ProtoReflect.Descriptor instead.
func (*FeeResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{22}
}

func (x *FeeResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *FeeResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *FeeResponse) GetSlowFee() string {
	if x != nil {
		return x.SlowFee
	}
	return ""
}

func (x *FeeResponse) GetNormalFee() string {
	if x != nil {
		return x.NormalFee
	}
	return ""
}

func (x *FeeResponse) GetFastFee() string {
	if x != nil {
		return x.FastFee
	}
	return ""
}

type SendTxRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Coin          string                 `protobuf:"bytes,3,opt,name=coin,proto3" json:"coin,omitempty"`
	Network       string                 `protobuf:"bytes,4,opt,name=network,proto3" json:"network,omitempty"`
	RawTx         string                 `protobuf:"bytes,5,opt,name=raw_tx,json=rawTx,proto3" json:"raw_tx,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendTxRequest) Reset() {
	*x = SendTxRequest{}
	mi := &file_dapplink_account_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendTxRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendTxRequest) ProtoMessage() {}

func (x *SendTxRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendTxRequest.ProtoReflect.Descriptor instead.
func (*SendTxRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{23}
}

func (x *SendTxRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *SendTxRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *SendTxRequest) GetCoin() string {
	if x != nil {
		return x.Coin
	}
	return ""
}

func (x *SendTxRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *SendTxRequest) GetRawTx() string {
	if x != nil {
		return x.RawTx
	}
	return ""
}

type SendTxResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	TxHash        string                 `protobuf:"bytes,3,opt,name=tx_hash,json=txHash,proto3" json:"tx_hash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendTxResponse) Reset() {
	*x = SendTxResponse{}
	mi := &file_dapplink_account_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendTxResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendTxResponse) ProtoMessage() {}

func (x *SendTxResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendTxResponse.ProtoReflect.Descriptor instead.
func (*SendTxResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{24}
}

func (x *SendTxResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *SendTxResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SendTxResponse) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

type TxAddressRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken   string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain           string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Coin            string                 `protobuf:"bytes,3,opt,name=coin,proto3" json:"coin,omitempty"`
	Network         string                 `protobuf:"bytes,4,opt,name=network,proto3" json:"network,omitempty"`
	Address         string                 `protobuf:"bytes,5,opt,name=address,proto3" json:"address,omitempty"`
	ContractAddress string                 `protobuf:"bytes,6,opt,name=contract_address,json=contractAddress,proto3" json:"contract_address,omitempty"`
	Page            uint32                 `protobuf:"varint,7,opt,name=page,proto3" json:"page,omitempty"`
	Pagesize        uint32                 `protobuf:"varint,8,opt,name=pagesize,proto3" json:"pagesize,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *TxAddressRequest) Reset() {
	*x = TxAddressRequest{}
	mi := &file_dapplink_account_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TxAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxAddressRequest) ProtoMessage() {}

func (x *TxAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxAddressRequest.ProtoReflect.Descriptor instead.
func (*TxAddressRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{25}
}

func (x *TxAddressRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *TxAddressRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *TxAddressRequest) GetCoin() string {
	if x != nil {
		return x.Coin
	}
	return ""
}

func (x *TxAddressRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *TxAddressRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *TxAddressRequest) GetContractAddress() string {
	if x != nil {
		return x.ContractAddress
	}
	return ""
}

func (x *TxAddressRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *TxAddressRequest) GetPagesize() uint32 {
	if x != nil {
		return x.Pagesize
	}
	return 0
}

type TxAddressResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Tx            []*TxMessage           `protobuf:"bytes,3,rep,name=tx,proto3" json:"tx,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TxAddressResponse) Reset() {
	*x = TxAddressResponse{}
	mi := &file_dapplink_account_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TxAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxAddressResponse) ProtoMessage() {}

func (x *TxAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxAddressResponse.ProtoReflect.Descriptor instead.
func (*TxAddressResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{26}
}

func (x *TxAddressResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *TxAddressResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *TxAddressResponse) GetTx() []*TxMessage {
	if x != nil {
		return x.Tx
	}
	return nil
}

type TxHashRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Coin          string                 `protobuf:"bytes,3,opt,name=coin,proto3" json:"coin,omitempty"`
	Network       string                 `protobuf:"bytes,4,opt,name=network,proto3" json:"network,omitempty"`
	Hash          string                 `protobuf:"bytes,5,opt,name=hash,proto3" json:"hash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TxHashRequest) Reset() {
	*x = TxHashRequest{}
	mi := &file_dapplink_account_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TxHashRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxHashRequest) ProtoMessage() {}

func (x *TxHashRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxHashRequest.ProtoReflect.Descriptor instead.
func (*TxHashRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{27}
}

func (x *TxHashRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *TxHashRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *TxHashRequest) GetCoin() string {
	if x != nil {
		return x.Coin
	}
	return ""
}

func (x *TxHashRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *TxHashRequest) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

type TxHashResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Tx            *TxMessage             `protobuf:"bytes,3,opt,name=tx,proto3" json:"tx,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TxHashResponse) Reset() {
	*x = TxHashResponse{}
	mi := &file_dapplink_account_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TxHashResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxHashResponse) ProtoMessage() {}

func (x *TxHashResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxHashResponse.ProtoReflect.Descriptor instead.
func (*TxHashResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{28}
}

func (x *TxHashResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *TxHashResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *TxHashResponse) GetTx() *TxMessage {
	if x != nil {
		return x.Tx
	}
	return nil
}

type UnSignTransactionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
	Base64Tx      string                 `protobuf:"bytes,4,opt,name=base64_tx,json=base64Tx,proto3" json:"base64_tx,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnSignTransactionRequest) Reset() {
	*x = UnSignTransactionRequest{}
	mi := &file_dapplink_account_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnSignTransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnSignTransactionRequest) ProtoMessage() {}

func (x *UnSignTransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnSignTransactionRequest.ProtoReflect.Descriptor instead.
func (*UnSignTransactionRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{29}
}

func (x *UnSignTransactionRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *UnSignTransactionRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *UnSignTransactionRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *UnSignTransactionRequest) GetBase64Tx() string {
	if x != nil {
		return x.Base64Tx
	}
	return ""
}

type UnSignTransactionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	UnSignTx      string                 `protobuf:"bytes,3,opt,name=un_sign_tx,json=unSignTx,proto3" json:"un_sign_tx,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnSignTransactionResponse) Reset() {
	*x = UnSignTransactionResponse{}
	mi := &file_dapplink_account_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnSignTransactionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnSignTransactionResponse) ProtoMessage() {}

func (x *UnSignTransactionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnSignTransactionResponse.ProtoReflect.Descriptor instead.
func (*UnSignTransactionResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{30}
}

func (x *UnSignTransactionResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *UnSignTransactionResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UnSignTransactionResponse) GetUnSignTx() string {
	if x != nil {
		return x.UnSignTx
	}
	return ""
}

type SignedTransactionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
	Base64Tx      string                 `protobuf:"bytes,4,opt,name=base64_tx,json=base64Tx,proto3" json:"base64_tx,omitempty"`
	Signature     string                 `protobuf:"bytes,5,opt,name=signature,proto3" json:"signature,omitempty"`
	PublicKey     string                 `protobuf:"bytes,6,opt,name=public_key,json=publicKey,proto3" json:"public_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignedTransactionRequest) Reset() {
	*x = SignedTransactionRequest{}
	mi := &file_dapplink_account_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignedTransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignedTransactionRequest) ProtoMessage() {}

func (x *SignedTransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignedTransactionRequest.ProtoReflect.Descriptor instead.
func (*SignedTransactionRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{31}
}

func (x *SignedTransactionRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *SignedTransactionRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *SignedTransactionRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *SignedTransactionRequest) GetBase64Tx() string {
	if x != nil {
		return x.Base64Tx
	}
	return ""
}

func (x *SignedTransactionRequest) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

func (x *SignedTransactionRequest) GetPublicKey() string {
	if x != nil {
		return x.PublicKey
	}
	return ""
}

type SignedTransactionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	SignedTx      string                 `protobuf:"bytes,3,opt,name=signed_tx,json=signedTx,proto3" json:"signed_tx,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignedTransactionResponse) Reset() {
	*x = SignedTransactionResponse{}
	mi := &file_dapplink_account_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignedTransactionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignedTransactionResponse) ProtoMessage() {}

func (x *SignedTransactionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignedTransactionResponse.ProtoReflect.Descriptor instead.
func (*SignedTransactionResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{32}
}

func (x *SignedTransactionResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *SignedTransactionResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SignedTransactionResponse) GetSignedTx() string {
	if x != nil {
		return x.SignedTx
	}
	return ""
}

type VerifyTransactionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
	PublicKey     string                 `protobuf:"bytes,4,opt,name=public_key,json=publicKey,proto3" json:"public_key,omitempty"`
	Signature     string                 `protobuf:"bytes,5,opt,name=signature,proto3" json:"signature,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifyTransactionRequest) Reset() {
	*x = VerifyTransactionRequest{}
	mi := &file_dapplink_account_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyTransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyTransactionRequest) ProtoMessage() {}

func (x *VerifyTransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyTransactionRequest.ProtoReflect.Descriptor instead.
func (*VerifyTransactionRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{33}
}

func (x *VerifyTransactionRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *VerifyTransactionRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *VerifyTransactionRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *VerifyTransactionRequest) GetPublicKey() string {
	if x != nil {
		return x.PublicKey
	}
	return ""
}

func (x *VerifyTransactionRequest) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

type VerifyTransactionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Verify        bool                   `protobuf:"varint,3,opt,name=verify,proto3" json:"verify,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifyTransactionResponse) Reset() {
	*x = VerifyTransactionResponse{}
	mi := &file_dapplink_account_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyTransactionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyTransactionResponse) ProtoMessage() {}

func (x *VerifyTransactionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyTransactionResponse.ProtoReflect.Descriptor instead.
func (*VerifyTransactionResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{34}
}

func (x *VerifyTransactionResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *VerifyTransactionResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *VerifyTransactionResponse) GetVerify() bool {
	if x != nil {
		return x.Verify
	}
	return false
}

type DecodeTransactionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
	RawTx         string                 `protobuf:"bytes,4,opt,name=raw_tx,json=rawTx,proto3" json:"raw_tx,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DecodeTransactionRequest) Reset() {
	*x = DecodeTransactionRequest{}
	mi := &file_dapplink_account_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DecodeTransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DecodeTransactionRequest) ProtoMessage() {}

func (x *DecodeTransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DecodeTransactionRequest.ProtoReflect.Descriptor instead.
func (*DecodeTransactionRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{35}
}

func (x *DecodeTransactionRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *DecodeTransactionRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *DecodeTransactionRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *DecodeTransactionRequest) GetRawTx() string {
	if x != nil {
		return x.RawTx
	}
	return ""
}

type DecodeTransactionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Base64Tx      string                 `protobuf:"bytes,3,opt,name=base64_tx,json=base64Tx,proto3" json:"base64_tx,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DecodeTransactionResponse) Reset() {
	*x = DecodeTransactionResponse{}
	mi := &file_dapplink_account_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DecodeTransactionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DecodeTransactionResponse) ProtoMessage() {}

func (x *DecodeTransactionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DecodeTransactionResponse.ProtoReflect.Descriptor instead.
func (*DecodeTransactionResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{36}
}

func (x *DecodeTransactionResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *DecodeTransactionResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *DecodeTransactionResponse) GetBase64Tx() string {
	if x != nil {
		return x.Base64Tx
	}
	return ""
}

type ExtraDataRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
	Address       string                 `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	Coin          string                 `protobuf:"bytes,5,opt,name=coin,proto3" json:"coin,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExtraDataRequest) Reset() {
	*x = ExtraDataRequest{}
	mi := &file_dapplink_account_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExtraDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtraDataRequest) ProtoMessage() {}

func (x *ExtraDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtraDataRequest.ProtoReflect.Descriptor instead.
func (*ExtraDataRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{37}
}

func (x *ExtraDataRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *ExtraDataRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *ExtraDataRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *ExtraDataRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *ExtraDataRequest) GetCoin() string {
	if x != nil {
		return x.Coin
	}
	return ""
}

type ExtraDataResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Value         string                 `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExtraDataResponse) Reset() {
	*x = ExtraDataResponse{}
	mi := &file_dapplink_account_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExtraDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtraDataResponse) ProtoMessage() {}

func (x *ExtraDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtraDataResponse.ProtoReflect.Descriptor instead.
func (*ExtraDataResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{38}
}

func (x *ExtraDataResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *ExtraDataResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ExtraDataResponse) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type NftMessage struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	TokenContractAddress string                 `protobuf:"bytes,1,opt,name=token_contract_address,json=tokenContractAddress,proto3" json:"token_contract_address,omitempty"`
	TokenId              string                 `protobuf:"bytes,2,opt,name=token_id,json=tokenId,proto3" json:"token_id,omitempty"`
	Amount               string                 `protobuf:"bytes,3,opt,name=amount,proto3" json:"amount,omitempty"`
	TokenName            string                 `protobuf:"bytes,4,opt,name=token_name,json=tokenName,proto3" json:"token_name,omitempty"`
	TokenUrl             string                 `protobuf:"bytes,5,opt,name=token_url,json=tokenUrl,proto3" json:"token_url,omitempty"`
	Description          string                 `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	MetaData             string                 `protobuf:"bytes,7,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *NftMessage) Reset() {
	*x = NftMessage{}
	mi := &file_dapplink_account_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NftMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NftMessage) ProtoMessage() {}

func (x *NftMessage) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NftMessage.ProtoReflect.Descriptor instead.
func (*NftMessage) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{39}
}

func (x *NftMessage) GetTokenContractAddress() string {
	if x != nil {
		return x.TokenContractAddress
	}
	return ""
}

func (x *NftMessage) GetTokenId() string {
	if x != nil {
		return x.TokenId
	}
	return ""
}

func (x *NftMessage) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *NftMessage) GetTokenName() string {
	if x != nil {
		return x.TokenName
	}
	return ""
}

func (x *NftMessage) GetTokenUrl() string {
	if x != nil {
		return x.TokenUrl
	}
	return ""
}

func (x *NftMessage) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *NftMessage) GetMetaData() string {
	if x != nil {
		return x.MetaData
	}
	return ""
}

type NftAddressRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken   string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain           string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Network         string                 `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
	Address         string                 `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	ProtocolType    string                 `protobuf:"bytes,5,opt,name=protocol_type,json=protocolType,proto3" json:"protocol_type,omitempty"`
	ContractAddress string                 `protobuf:"bytes,6,opt,name=contract_address,json=contractAddress,proto3" json:"contract_address,omitempty"`
	Page            uint32                 `protobuf:"varint,7,opt,name=page,proto3" json:"page,omitempty"`
	Pagesize        uint32                 `protobuf:"varint,8,opt,name=pagesize,proto3" json:"pagesize,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *NftAddressRequest) Reset() {
	*x = NftAddressRequest{}
	mi := &file_dapplink_account_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NftAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NftAddressRequest) ProtoMessage() {}

func (x *NftAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NftAddressRequest.ProtoReflect.Descriptor instead.
func (*NftAddressRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{40}
}

func (x *NftAddressRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *NftAddressRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *NftAddressRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *NftAddressRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *NftAddressRequest) GetProtocolType() string {
	if x != nil {
		return x.ProtocolType
	}
	return ""
}

func (x *NftAddressRequest) GetContractAddress() string {
	if x != nil {
		return x.ContractAddress
	}
	return ""
}

func (x *NftAddressRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *NftAddressRequest) GetPagesize() uint32 {
	if x != nil {
		return x.Pagesize
	}
	return 0
}

type NftAddressResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	NftInfo       []*NftMessage          `protobuf:"bytes,3,rep,name=nft_info,json=nftInfo,proto3" json:"nft_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NftAddressResponse) Reset() {
	*x = NftAddressResponse{}
	mi := &file_dapplink_account_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NftAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NftAddressResponse) ProtoMessage() {}

func (x *NftAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NftAddressResponse.ProtoReflect.Descriptor instead.
func (*NftAddressResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{41}
}

func (x *NftAddressResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *NftAddressResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *NftAddressResponse) GetNftInfo() []*NftMessage {
	if x != nil {
		return x.NftInfo
	}
	return nil
}

type NftCollectionMessage struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	TokenId              string                 `protobuf:"bytes,1,opt,name=token_id,json=tokenId,proto3" json:"token_id,omitempty"`
	HoldingAddressAmount string                 `protobuf:"bytes,2,opt,name=holding_address_amount,json=holdingAddressAmount,proto3" json:"holding_address_amount,omitempty"`
	TokenUrl             string                 `protobuf:"bytes,3,opt,name=token_url,json=tokenUrl,proto3" json:"token_url,omitempty"`
	ProtocolType         string                 `protobuf:"bytes,4,opt,name=protocol_type,json=protocolType,proto3" json:"protocol_type,omitempty"`
	LastTransactionTime  string                 `protobuf:"bytes,5,opt,name=last_transaction_time,json=lastTransactionTime,proto3" json:"last_transaction_time,omitempty"`
	LastPrice            string                 `protobuf:"bytes,6,opt,name=last_price,json=lastPrice,proto3" json:"last_price,omitempty"`
	LastPriceUnit        string                 `protobuf:"bytes,7,opt,name=last_price_unit,json=lastPriceUnit,proto3" json:"last_price_unit,omitempty"`
	TransactionCount     string                 `protobuf:"bytes,8,opt,name=transaction_count,json=transactionCount,proto3" json:"transaction_count,omitempty"`
	MintTime             string                 `protobuf:"bytes,9,opt,name=mint_time,json=mintTime,proto3" json:"mint_time,omitempty"`
	Title                string                 `protobuf:"bytes,10,opt,name=title,proto3" json:"title,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *NftCollectionMessage) Reset() {
	*x = NftCollectionMessage{}
	mi := &file_dapplink_account_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NftCollectionMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NftCollectionMessage) ProtoMessage() {}

func (x *NftCollectionMessage) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NftCollectionMessage.ProtoReflect.Descriptor instead.
func (*NftCollectionMessage) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{42}
}

func (x *NftCollectionMessage) GetTokenId() string {
	if x != nil {
		return x.TokenId
	}
	return ""
}

func (x *NftCollectionMessage) GetHoldingAddressAmount() string {
	if x != nil {
		return x.HoldingAddressAmount
	}
	return ""
}

func (x *NftCollectionMessage) GetTokenUrl() string {
	if x != nil {
		return x.TokenUrl
	}
	return ""
}

func (x *NftCollectionMessage) GetProtocolType() string {
	if x != nil {
		return x.ProtocolType
	}
	return ""
}

func (x *NftCollectionMessage) GetLastTransactionTime() string {
	if x != nil {
		return x.LastTransactionTime
	}
	return ""
}

func (x *NftCollectionMessage) GetLastPrice() string {
	if x != nil {
		return x.LastPrice
	}
	return ""
}

func (x *NftCollectionMessage) GetLastPriceUnit() string {
	if x != nil {
		return x.LastPriceUnit
	}
	return ""
}

func (x *NftCollectionMessage) GetTransactionCount() string {
	if x != nil {
		return x.TransactionCount
	}
	return ""
}

func (x *NftCollectionMessage) GetMintTime() string {
	if x != nil {
		return x.MintTime
	}
	return ""
}

func (x *NftCollectionMessage) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

type NftCollectionRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken        string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain                string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Network              string                 `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
	TokenContractAddress string                 `protobuf:"bytes,4,opt,name=token_contract_address,json=tokenContractAddress,proto3" json:"token_contract_address,omitempty"`
	FilterType           string                 `protobuf:"bytes,5,opt,name=filter_type,json=filterType,proto3" json:"filter_type,omitempty"`
	TokenId              string                 `protobuf:"bytes,6,opt,name=token_id,json=tokenId,proto3" json:"token_id,omitempty"`
	Page                 uint32                 `protobuf:"varint,7,opt,name=page,proto3" json:"page,omitempty"`
	Pagesize             uint32                 `protobuf:"varint,8,opt,name=pagesize,proto3" json:"pagesize,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *NftCollectionRequest) Reset() {
	*x = NftCollectionRequest{}
	mi := &file_dapplink_account_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NftCollectionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NftCollectionRequest) ProtoMessage() {}

func (x *NftCollectionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NftCollectionRequest.ProtoReflect.Descriptor instead.
func (*NftCollectionRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{43}
}

func (x *NftCollectionRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *NftCollectionRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *NftCollectionRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *NftCollectionRequest) GetTokenContractAddress() string {
	if x != nil {
		return x.TokenContractAddress
	}
	return ""
}

func (x *NftCollectionRequest) GetFilterType() string {
	if x != nil {
		return x.FilterType
	}
	return ""
}

func (x *NftCollectionRequest) GetTokenId() string {
	if x != nil {
		return x.TokenId
	}
	return ""
}

func (x *NftCollectionRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *NftCollectionRequest) GetPagesize() uint32 {
	if x != nil {
		return x.Pagesize
	}
	return 0
}

type NftCollectionResponse struct {
	state                protoimpl.MessageState  `protogen:"open.v1"`
	Code                 common.ReturnCode       `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg                  string                  `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	NftCollectionMessage []*NftCollectionMessage `protobuf:"bytes,3,rep,name=nft_collection_message,json=nftCollectionMessage,proto3" json:"nft_collection_message,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *NftCollectionResponse) Reset() {
	*x = NftCollectionResponse{}
	mi := &file_dapplink_account_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NftCollectionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NftCollectionResponse) ProtoMessage() {}

func (x *NftCollectionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NftCollectionResponse.ProtoReflect.Descriptor instead.
func (*NftCollectionResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{44}
}

func (x *NftCollectionResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *NftCollectionResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *NftCollectionResponse) GetNftCollectionMessage() []*NftCollectionMessage {
	if x != nil {
		return x.NftCollectionMessage
	}
	return nil
}

type NftDetailRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NftDetailRequest) Reset() {
	*x = NftDetailRequest{}
	mi := &file_dapplink_account_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NftDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NftDetailRequest) ProtoMessage() {}

func (x *NftDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NftDetailRequest.ProtoReflect.Descriptor instead.
func (*NftDetailRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{45}
}

type NftDetailResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NftDetailResponse) Reset() {
	*x = NftDetailResponse{}
	mi := &file_dapplink_account_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NftDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NftDetailResponse) ProtoMessage() {}

func (x *NftDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NftDetailResponse.ProtoReflect.Descriptor instead.
func (*NftDetailResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{46}
}

type NftHolderListRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NftHolderListRequest) Reset() {
	*x = NftHolderListRequest{}
	mi := &file_dapplink_account_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NftHolderListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NftHolderListRequest) ProtoMessage() {}

func (x *NftHolderListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NftHolderListRequest.ProtoReflect.Descriptor instead.
func (*NftHolderListRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{47}
}

type NftHolderListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NftHolderListResponse) Reset() {
	*x = NftHolderListResponse{}
	mi := &file_dapplink_account_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NftHolderListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NftHolderListResponse) ProtoMessage() {}

func (x *NftHolderListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NftHolderListResponse.ProtoReflect.Descriptor instead.
func (*NftHolderListResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{48}
}

type NftTradeHistoryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NftTradeHistoryRequest) Reset() {
	*x = NftTradeHistoryRequest{}
	mi := &file_dapplink_account_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NftTradeHistoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NftTradeHistoryRequest) ProtoMessage() {}

func (x *NftTradeHistoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NftTradeHistoryRequest.ProtoReflect.Descriptor instead.
func (*NftTradeHistoryRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{49}
}

type NftTradeHistoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NftTradeHistoryResponse) Reset() {
	*x = NftTradeHistoryResponse{}
	mi := &file_dapplink_account_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NftTradeHistoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NftTradeHistoryResponse) ProtoMessage() {}

func (x *NftTradeHistoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NftTradeHistoryResponse.ProtoReflect.Descriptor instead.
func (*NftTradeHistoryResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{50}
}

type AddressNftTradeHistoryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddressNftTradeHistoryRequest) Reset() {
	*x = AddressNftTradeHistoryRequest{}
	mi := &file_dapplink_account_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddressNftTradeHistoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddressNftTradeHistoryRequest) ProtoMessage() {}

func (x *AddressNftTradeHistoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddressNftTradeHistoryRequest.ProtoReflect.Descriptor instead.
func (*AddressNftTradeHistoryRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{51}
}

type AddressNftTradeHistoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddressNftTradeHistoryResponse) Reset() {
	*x = AddressNftTradeHistoryResponse{}
	mi := &file_dapplink_account_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddressNftTradeHistoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddressNftTradeHistoryResponse) ProtoMessage() {}

func (x *AddressNftTradeHistoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_account_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddressNftTradeHistoryResponse.ProtoReflect.Descriptor instead.
func (*AddressNftTradeHistoryResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_account_proto_rawDescGZIP(), []int{52}
}

var File_dapplink_account_proto protoreflect.FileDescriptor

var file_dapplink_account_proto_rawDesc = string([]byte{
	0x0a, 0x16, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0x15, 0x64, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xbc, 0x02, 0x0a, 0x09, 0x54, 0x78, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68,
	0x61, 0x73, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x72, 0x6f,
	0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x0e, 0x0a,
	0x02, 0x74, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x74, 0x6f, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x66, 0x65, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x66, 0x65, 0x65, 0x12, 0x32, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x54, 0x78, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x68,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x22, 0x7a, 0x0a, 0x09, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a,
	0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73,
	0x68, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x73, 0x65, 0x46, 0x65, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x62, 0x61, 0x73, 0x65, 0x46, 0x65, 0x65, 0x12, 0x3f, 0x0a, 0x0c, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x54, 0x78, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x0c,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xdd, 0x04, 0x0a,
	0x0b, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04,
	0x68, 0x61, 0x73, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68,
	0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x48, 0x61, 0x73,
	0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x6e, 0x63, 0x6c, 0x65, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x6e, 0x63, 0x6c, 0x65, 0x48, 0x61, 0x73, 0x68,
	0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x69, 0x6e, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x69, 0x6e, 0x42, 0x61, 0x73, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x72, 0x6f, 0x6f, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6f,
	0x74, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x78, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x74, 0x78, 0x48, 0x61, 0x73, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65,
	0x63, 0x65, 0x69, 0x70, 0x74, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x72, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x48, 0x61, 0x73, 0x68, 0x12, 0x2c, 0x0a,
	0x12, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x65, 0x61, 0x63, 0x6f, 0x6e, 0x5f, 0x72,
	0x6f, 0x6f, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x70, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x42, 0x65, 0x61, 0x63, 0x6f, 0x6e, 0x52, 0x6f, 0x6f, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x64,
	0x69, 0x66, 0x66, 0x69, 0x63, 0x75, 0x6c, 0x74, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x64, 0x69, 0x66, 0x66, 0x69, 0x63, 0x75, 0x6c, 0x74, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x73, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x67, 0x61, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x12, 0x19, 0x0a, 0x08, 0x67, 0x61, 0x73, 0x5f, 0x75, 0x73, 0x65, 0x64, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x07, 0x67, 0x61, 0x73, 0x55, 0x73, 0x65, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x69, 0x78, 0x5f, 0x64, 0x69, 0x67,
	0x65, 0x73, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x69, 0x78, 0x44, 0x69,
	0x67, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x61,
	0x73, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x61,
	0x73, 0x65, 0x46, 0x65, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x77, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61,
	0x77, 0x61, 0x6c, 0x73, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x77, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x6c, 0x73, 0x48, 0x61, 0x73, 0x68,
	0x12, 0x22, 0x0a, 0x0d, 0x62, 0x6c, 0x6f, 0x62, 0x5f, 0x67, 0x61, 0x73, 0x5f, 0x75, 0x73, 0x65,
	0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x62, 0x6c, 0x6f, 0x62, 0x47, 0x61, 0x73,
	0x55, 0x73, 0x65, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x65, 0x78, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x62,
	0x6c, 0x6f, 0x62, 0x5f, 0x67, 0x61, 0x73, 0x18, 0x14, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x65,
	0x78, 0x63, 0x65, 0x73, 0x73, 0x42, 0x6c, 0x6f, 0x62, 0x47, 0x61, 0x73, 0x22, 0xf1, 0x01, 0x0a,
	0x03, 0x4c, 0x6f, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x16,
	0x0a, 0x06, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06,
	0x74, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x17, 0x0a,
	0x07, 0x74, 0x78, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x74, 0x78, 0x48, 0x61, 0x73, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x78, 0x5f, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x74, 0x78, 0x49, 0x6e, 0x64, 0x65,
	0x78, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x61, 0x73, 0x68,
	0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65,
	0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x64,
	0x22, 0x6d, 0x0a, 0x14, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x68, 0x61, 0x69, 0x6e,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12,
	0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x22,
	0x6d, 0x0a, 0x15, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x22, 0xa1,
	0x01, 0x0a, 0x15, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12,
	0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x6b, 0x65,
	0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b,
	0x65, 0x79, 0x22, 0x6e, 0x0a, 0x16, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x22, 0x86, 0x01, 0x0a, 0x13, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x68, 0x0a, 0x14, 0x56,
	0x61, 0x6c, 0x69, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74,
	0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x22, 0x82, 0x01, 0x0a, 0x12, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e,
	0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x12, 0x17, 0x0a, 0x07, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x74, 0x78, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x76, 0x69, 0x65, 0x77, 0x54, 0x78, 0x22, 0x7c, 0x0a, 0x10, 0x42, 0x6c,
	0x6f, 0x63, 0x6b, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25,
	0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x68,
	0x61, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x12,
	0x17, 0x0a, 0x07, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x74, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x76, 0x69, 0x65, 0x77, 0x54, 0x78, 0x22, 0xd0, 0x01, 0x0a, 0x18, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x6f, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x74, 0x6f, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x27,
	0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x5f, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x68, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xe2, 0x01, 0x0a, 0x0d,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64,
	0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x68, 0x61, 0x73, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x66, 0x65,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x61, 0x73, 0x65, 0x46, 0x65, 0x65,
	0x12, 0x4e, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x49,
	0x6e, 0x66, 0x6f, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x22, 0x83, 0x01, 0x0a, 0x16, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x22, 0x89, 0x01, 0x0a, 0x18, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68,
	0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x22, 0x93, 0x01, 0x0a, 0x13, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x40, 0x0a, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x64,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0b, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x94, 0x01, 0x0a, 0x13, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x42, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a,
	0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a,
	0x03, 0x65, 0x6e, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x22,
	0x94, 0x01, 0x0a, 0x14, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x42, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x12, 0x40, 0x0a, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x64, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x42, 0x6c,
	0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xee, 0x01, 0x0a, 0x0e, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x69, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x29,
	0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x70, 0x72, 0x6f,
	0x70, 0x6f, 0x73, 0x65, 0x72, 0x5f, 0x6b, 0x65, 0x79, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x70, 0x72, 0x6f, 0x70, 0x6f, 0x73, 0x65, 0x72, 0x4b,
	0x65, 0x79, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0xc4, 0x01, 0x0a, 0x0f, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x71, 0x75,
	0x65, 0x6e, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x71, 0x75,
	0x65, 0x6e, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x22, 0xa7,
	0x01, 0x0a, 0x0a, 0x46, 0x65, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a,
	0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x69, 0x6e, 0x12, 0x18,
	0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x61, 0x77, 0x54,
	0x78, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x61, 0x77, 0x54, 0x78, 0x12, 0x18,
	0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x9e, 0x01, 0x0a, 0x0b, 0x46, 0x65, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x6c, 0x6f, 0x77, 0x5f, 0x66, 0x65, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x6c, 0x6f, 0x77, 0x46, 0x65, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x46, 0x65, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x66, 0x61, 0x73, 0x74, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x66, 0x61, 0x73, 0x74, 0x46, 0x65, 0x65, 0x22, 0x91, 0x01, 0x0a, 0x0d, 0x53, 0x65,
	0x6e, 0x64, 0x54, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x69, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x61, 0x77, 0x5f, 0x74, 0x78,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x61, 0x77, 0x54, 0x78, 0x22, 0x65, 0x0a,
	0x0e, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x78, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e,
	0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43,
	0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x17, 0x0a, 0x07, 0x74,
	0x78, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x78,
	0x48, 0x61, 0x73, 0x68, 0x22, 0xf2, 0x01, 0x0a, 0x10, 0x54, 0x78, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x69, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x29,
	0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x7c, 0x0a, 0x11, 0x54, 0x78, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f,
	0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x2b, 0x0a, 0x02, 0x74, 0x78,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x54, 0x78, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x52, 0x02, 0x74, 0x78, 0x22, 0x8e, 0x01, 0x0a, 0x0d, 0x54, 0x78, 0x48, 0x61,
	0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x69, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x22, 0x79, 0x0a, 0x0e, 0x54, 0x78, 0x48, 0x61,
	0x73, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x2b, 0x0a, 0x02, 0x74, 0x78, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x54, 0x78, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52,
	0x02, 0x74, 0x78, 0x22, 0x8e, 0x01, 0x0a, 0x18, 0x55, 0x6e, 0x53, 0x69, 0x67, 0x6e, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a,
	0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x73, 0x65, 0x36,
	0x34, 0x5f, 0x74, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x61, 0x73, 0x65,
	0x36, 0x34, 0x54, 0x78, 0x22, 0x75, 0x0a, 0x19, 0x55, 0x6e, 0x53, 0x69, 0x67, 0x6e, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72,
	0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x1c, 0x0a,
	0x0a, 0x75, 0x6e, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x74, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x75, 0x6e, 0x53, 0x69, 0x67, 0x6e, 0x54, 0x78, 0x22, 0xcb, 0x01, 0x0a, 0x18,
	0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12,
	0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12,
	0x1b, 0x0a, 0x09, 0x62, 0x61, 0x73, 0x65, 0x36, 0x34, 0x5f, 0x74, 0x78, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x62, 0x61, 0x73, 0x65, 0x36, 0x34, 0x54, 0x78, 0x12, 0x1c, 0x0a, 0x09,
	0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x63, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b, 0x65, 0x79, 0x22, 0x74, 0x0a, 0x19, 0x53, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d,
	0x73, 0x67, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x74, 0x78, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x78, 0x22,
	0xae, 0x01, 0x0a, 0x18, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e,
	0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x6b, 0x65,
	0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b,
	0x65, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x22, 0x6f, 0x0a, 0x19, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64,
	0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x65, 0x72,
	0x69, 0x66, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x76, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x22, 0x88, 0x01, 0x0a, 0x18, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25,
	0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x61, 0x77, 0x5f, 0x74, 0x78, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x61, 0x77, 0x54, 0x78, 0x22, 0x74, 0x0a, 0x19,
	0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x73, 0x65, 0x36, 0x34, 0x5f,
	0x74, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x61, 0x73, 0x65, 0x36, 0x34,
	0x54, 0x78, 0x22, 0x97, 0x01, 0x0a, 0x10, 0x45, 0x78, 0x74, 0x72, 0x61, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14,
	0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x18,
	0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x69, 0x6e,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x69, 0x6e, 0x22, 0x65, 0x0a, 0x11,
	0x45, 0x78, 0x74, 0x72, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72,
	0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x22, 0xf0, 0x01, 0x0a, 0x0a, 0x4e, 0x66, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x14, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x74,
	0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65,
	0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x22, 0x84, 0x02, 0x0a, 0x11, 0x4e, 0x66, 0x74, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e,
	0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x23, 0x0a,
	0x0d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x5f, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x89, 0x01,
	0x0a, 0x12, 0x4e, 0x66, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65,
	0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67,
	0x12, 0x37, 0x0a, 0x08, 0x6e, 0x66, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4e, 0x66, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x52, 0x07, 0x6e, 0x66, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x84, 0x03, 0x0a, 0x14, 0x4e, 0x66,
	0x74, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x34, 0x0a,
	0x16, 0x68, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x68,
	0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x55, 0x72, 0x6c,
	0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x6c, 0x61, 0x73, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6c,
	0x61, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74,
	0x12, 0x2b, 0x0a, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1b, 0x0a,
	0x09, 0x6d, 0x69, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6d, 0x69, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x22, 0x8f, 0x02, 0x0a, 0x14, 0x4e, 0x66, 0x74, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x12, 0x34, 0x0a, 0x16, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x14, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x73, 0x69,
	0x7a, 0x65, 0x22, 0xb1, 0x01, 0x0a, 0x15, 0x4e, 0x66, 0x74, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x5c, 0x0a, 0x16, 0x6e, 0x66, 0x74, 0x5f,
	0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4e, 0x66, 0x74, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x52, 0x14, 0x6e, 0x66, 0x74, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x12, 0x0a, 0x10, 0x4e, 0x66, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x13, 0x0a, 0x11, 0x4e, 0x66,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x16, 0x0a, 0x14, 0x4e, 0x66, 0x74, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x17, 0x0a, 0x15, 0x4e, 0x66, 0x74, 0x48, 0x6f,
	0x6c, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x18, 0x0a, 0x16, 0x4e, 0x66, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x19, 0x0a, 0x17, 0x4e, 0x66,
	0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1f, 0x0a, 0x1d, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x4e, 0x66, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x20, 0x0a, 0x1e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x4e, 0x66, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2a, 0x64, 0x0a, 0x08, 0x54, 0x78, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x0c, 0x0a, 0x08, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64,
	0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x10, 0x01, 0x12,
	0x0a, 0x0a, 0x06, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x53,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x10, 0x03, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x6f, 0x6e, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x46, 0x61, 0x69, 0x6c, 0x65,
	0x64, 0x10, 0x04, 0x12, 0x09, 0x0a, 0x05, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x10, 0x05, 0x32, 0x85,
	0x13, 0x0a, 0x14, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x65, 0x0a, 0x10, 0x67, 0x65, 0x74, 0x53, 0x75,
	0x70, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x73, 0x12, 0x26, 0x2e, 0x64, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53,
	0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x68,
	0x61, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x65,
	0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x27, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x64, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5f, 0x0a, 0x0c, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x25, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x64,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x56, 0x61, 0x6c, 0x69, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5b, 0x0a, 0x10, 0x67, 0x65, 0x74, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x42, 0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x24, 0x2e, 0x64, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x42, 0x6c,
	0x6f, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1f, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x57, 0x0a, 0x0e, 0x67, 0x65, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x42,
	0x79, 0x48, 0x61, 0x73, 0x68, 0x12, 0x22, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x61,
	0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x64, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x69, 0x0a, 0x14,
	0x67, 0x65, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x79,
	0x48, 0x61, 0x73, 0x68, 0x12, 0x28, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25,
	0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6d, 0x0a, 0x16, 0x67, 0x65, 0x74, 0x42, 0x6c,
	0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x2a, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e,
	0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x68, 0x0a, 0x15, 0x67, 0x65, 0x74, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12,
	0x25, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x42, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x42,
	0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x53, 0x0a, 0x0a, 0x67, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x20,
	0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x21, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x47, 0x0a, 0x06, 0x67, 0x65, 0x74, 0x46, 0x65, 0x65, 0x12,
	0x1c, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x46, 0x65, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e,
	0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x46, 0x65, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4d,
	0x0a, 0x06, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x78, 0x12, 0x1f, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x6e, 0x64,
	0x54, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x64, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x6e,
	0x64, 0x54, 0x78, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5b, 0x0a,
	0x0e, 0x67, 0x65, 0x74, 0x54, 0x78, 0x42, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x22, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x54, 0x78, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x54, 0x78, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x52, 0x0a, 0x0b, 0x67, 0x65,
	0x74, 0x54, 0x78, 0x42, 0x79, 0x48, 0x61, 0x73, 0x68, 0x12, 0x1f, 0x2e, 0x64, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x54, 0x78, 0x48,
	0x61, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x64, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x54, 0x78,
	0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x73,
	0x0a, 0x16, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x55, 0x6e, 0x53, 0x69, 0x67, 0x6e, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x55, 0x6e, 0x53, 0x69,
	0x67, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x55, 0x6e, 0x53, 0x69, 0x67, 0x6e, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x73, 0x0a, 0x16, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x53, 0x69, 0x67, 0x6e,
	0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x2e,
	0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x64, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6e, 0x0a, 0x11, 0x64, 0x65, 0x63, 0x6f,
	0x64, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x2e,
	0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x64, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x63,
	0x6f, 0x64, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x74, 0x0a, 0x17, 0x76, 0x65, 0x72, 0x69,
	0x66, 0x79, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2b, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x59,
	0x0a, 0x0c, 0x67, 0x65, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x22,
	0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x23, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x62, 0x0a, 0x13, 0x67, 0x65, 0x74,
	0x4e, 0x66, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x23, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x4e, 0x66, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4e, 0x66, 0x74, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x65, 0x0a,
	0x10, 0x67, 0x65, 0x74, 0x4e, 0x66, 0x74, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x26, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4e, 0x66, 0x74, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x64, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4e, 0x66, 0x74,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x59, 0x0a, 0x0c, 0x67, 0x65, 0x74, 0x4e, 0x66, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x12, 0x22, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4e, 0x66, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4e, 0x66, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x65, 0x0a, 0x10, 0x67, 0x65, 0x74, 0x4e, 0x66, 0x74, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x26, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4e, 0x66, 0x74, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x64, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4e,
	0x66, 0x74, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6b, 0x0a, 0x12, 0x67, 0x65, 0x74, 0x4e, 0x66, 0x74,
	0x54, 0x72, 0x61, 0x64, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x28, 0x2e, 0x64,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x4e, 0x66, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4e, 0x66, 0x74, 0x54, 0x72, 0x61,
	0x64, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x80, 0x01, 0x0a, 0x19, 0x67, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x4e, 0x66, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x12, 0x2f, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4e, 0x66, 0x74, 0x54,
	0x72, 0x61, 0x64, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x30, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4e, 0x66, 0x74,
	0x54, 0x72, 0x61, 0x64, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x27, 0x0a, 0x14, 0x78, 0x79, 0x7a, 0x2e, 0x64, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5a, 0x0f,
	0x2e, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_dapplink_account_proto_rawDescOnce sync.Once
	file_dapplink_account_proto_rawDescData []byte
)

func file_dapplink_account_proto_rawDescGZIP() []byte {
	file_dapplink_account_proto_rawDescOnce.Do(func() {
		file_dapplink_account_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_dapplink_account_proto_rawDesc), len(file_dapplink_account_proto_rawDesc)))
	})
	return file_dapplink_account_proto_rawDescData
}

var file_dapplink_account_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_dapplink_account_proto_msgTypes = make([]protoimpl.MessageInfo, 53)
var file_dapplink_account_proto_goTypes = []any{
	(TxStatus)(0),                          // 0: dapplink.account.TxStatus
	(*TxMessage)(nil),                      // 1: dapplink.account.TxMessage
	(*BlockData)(nil),                      // 2: dapplink.account.BlockData
	(*BlockHeader)(nil),                    // 3: dapplink.account.BlockHeader
	(*Log)(nil),                            // 4: dapplink.account.Log
	(*SupportChainsRequest)(nil),           // 5: dapplink.account.SupportChainsRequest
	(*SupportChainsResponse)(nil),          // 6: dapplink.account.SupportChainsResponse
	(*ConvertAddressRequest)(nil),          // 7: dapplink.account.ConvertAddressRequest
	(*ConvertAddressResponse)(nil),         // 8: dapplink.account.ConvertAddressResponse
	(*ValidAddressRequest)(nil),            // 9: dapplink.account.ValidAddressRequest
	(*ValidAddressResponse)(nil),           // 10: dapplink.account.ValidAddressResponse
	(*BlockNumberRequest)(nil),             // 11: dapplink.account.BlockNumberRequest
	(*BlockHashRequest)(nil),               // 12: dapplink.account.BlockHashRequest
	(*BlockInfoTransactionList)(nil),       // 13: dapplink.account.BlockInfoTransactionList
	(*BlockResponse)(nil),                  // 14: dapplink.account.BlockResponse
	(*BlockHeaderHashRequest)(nil),         // 15: dapplink.account.BlockHeaderHashRequest
	(*BlockHeaderNumberRequest)(nil),       // 16: dapplink.account.BlockHeaderNumberRequest
	(*BlockHeaderResponse)(nil),            // 17: dapplink.account.BlockHeaderResponse
	(*BlockByRangeRequest)(nil),            // 18: dapplink.account.BlockByRangeRequest
	(*BlockByRangeResponse)(nil),           // 19: dapplink.account.BlockByRangeResponse
	(*AccountRequest)(nil),                 // 20: dapplink.account.AccountRequest
	(*AccountResponse)(nil),                // 21: dapplink.account.AccountResponse
	(*FeeRequest)(nil),                     // 22: dapplink.account.FeeRequest
	(*FeeResponse)(nil),                    // 23: dapplink.account.FeeResponse
	(*SendTxRequest)(nil),                  // 24: dapplink.account.SendTxRequest
	(*SendTxResponse)(nil),                 // 25: dapplink.account.SendTxResponse
	(*TxAddressRequest)(nil),               // 26: dapplink.account.TxAddressRequest
	(*TxAddressResponse)(nil),              // 27: dapplink.account.TxAddressResponse
	(*TxHashRequest)(nil),                  // 28: dapplink.account.TxHashRequest
	(*TxHashResponse)(nil),                 // 29: dapplink.account.TxHashResponse
	(*UnSignTransactionRequest)(nil),       // 30: dapplink.account.UnSignTransactionRequest
	(*UnSignTransactionResponse)(nil),      // 31: dapplink.account.UnSignTransactionResponse
	(*SignedTransactionRequest)(nil),       // 32: dapplink.account.SignedTransactionRequest
	(*SignedTransactionResponse)(nil),      // 33: dapplink.account.SignedTransactionResponse
	(*VerifyTransactionRequest)(nil),       // 34: dapplink.account.VerifyTransactionRequest
	(*VerifyTransactionResponse)(nil),      // 35: dapplink.account.VerifyTransactionResponse
	(*DecodeTransactionRequest)(nil),       // 36: dapplink.account.DecodeTransactionRequest
	(*DecodeTransactionResponse)(nil),      // 37: dapplink.account.DecodeTransactionResponse
	(*ExtraDataRequest)(nil),               // 38: dapplink.account.ExtraDataRequest
	(*ExtraDataResponse)(nil),              // 39: dapplink.account.ExtraDataResponse
	(*NftMessage)(nil),                     // 40: dapplink.account.NftMessage
	(*NftAddressRequest)(nil),              // 41: dapplink.account.NftAddressRequest
	(*NftAddressResponse)(nil),             // 42: dapplink.account.NftAddressResponse
	(*NftCollectionMessage)(nil),           // 43: dapplink.account.NftCollectionMessage
	(*NftCollectionRequest)(nil),           // 44: dapplink.account.NftCollectionRequest
	(*NftCollectionResponse)(nil),          // 45: dapplink.account.NftCollectionResponse
	(*NftDetailRequest)(nil),               // 46: dapplink.account.NftDetailRequest
	(*NftDetailResponse)(nil),              // 47: dapplink.account.NftDetailResponse
	(*NftHolderListRequest)(nil),           // 48: dapplink.account.NftHolderListRequest
	(*NftHolderListResponse)(nil),          // 49: dapplink.account.NftHolderListResponse
	(*NftTradeHistoryRequest)(nil),         // 50: dapplink.account.NftTradeHistoryRequest
	(*NftTradeHistoryResponse)(nil),        // 51: dapplink.account.NftTradeHistoryResponse
	(*AddressNftTradeHistoryRequest)(nil),  // 52: dapplink.account.AddressNftTradeHistoryRequest
	(*AddressNftTradeHistoryResponse)(nil), // 53: dapplink.account.AddressNftTradeHistoryResponse
	(common.ReturnCode)(0),                 // 54: dapplink.ReturnCode
}
var file_dapplink_account_proto_depIdxs = []int32{
	0,  // 0: dapplink.account.TxMessage.status:type_name -> dapplink.account.TxStatus
	1,  // 1: dapplink.account.BlockData.transactions:type_name -> dapplink.account.TxMessage
	54, // 2: dapplink.account.SupportChainsResponse.code:type_name -> dapplink.ReturnCode
	54, // 3: dapplink.account.ConvertAddressResponse.code:type_name -> dapplink.ReturnCode
	54, // 4: dapplink.account.ValidAddressResponse.code:type_name -> dapplink.ReturnCode
	54, // 5: dapplink.account.BlockResponse.code:type_name -> dapplink.ReturnCode
	13, // 6: dapplink.account.BlockResponse.transactions:type_name -> dapplink.account.BlockInfoTransactionList
	54, // 7: dapplink.account.BlockHeaderResponse.code:type_name -> dapplink.ReturnCode
	3,  // 8: dapplink.account.BlockHeaderResponse.block_header:type_name -> dapplink.account.BlockHeader
	54, // 9: dapplink.account.BlockByRangeResponse.code:type_name -> dapplink.ReturnCode
	3,  // 10: dapplink.account.BlockByRangeResponse.block_header:type_name -> dapplink.account.BlockHeader
	54, // 11: dapplink.account.AccountResponse.code:type_name -> dapplink.ReturnCode
	54, // 12: dapplink.account.FeeResponse.code:type_name -> dapplink.ReturnCode
	54, // 13: dapplink.account.SendTxResponse.code:type_name -> dapplink.ReturnCode
	54, // 14: dapplink.account.TxAddressResponse.code:type_name -> dapplink.ReturnCode
	1,  // 15: dapplink.account.TxAddressResponse.tx:type_name -> dapplink.account.TxMessage
	54, // 16: dapplink.account.TxHashResponse.code:type_name -> dapplink.ReturnCode
	1,  // 17: dapplink.account.TxHashResponse.tx:type_name -> dapplink.account.TxMessage
	54, // 18: dapplink.account.UnSignTransactionResponse.code:type_name -> dapplink.ReturnCode
	54, // 19: dapplink.account.SignedTransactionResponse.code:type_name -> dapplink.ReturnCode
	54, // 20: dapplink.account.VerifyTransactionResponse.code:type_name -> dapplink.ReturnCode
	54, // 21: dapplink.account.DecodeTransactionResponse.code:type_name -> dapplink.ReturnCode
	54, // 22: dapplink.account.ExtraDataResponse.code:type_name -> dapplink.ReturnCode
	54, // 23: dapplink.account.NftAddressResponse.code:type_name -> dapplink.ReturnCode
	40, // 24: dapplink.account.NftAddressResponse.nft_info:type_name -> dapplink.account.NftMessage
	54, // 25: dapplink.account.NftCollectionResponse.code:type_name -> dapplink.ReturnCode
	43, // 26: dapplink.account.NftCollectionResponse.nft_collection_message:type_name -> dapplink.account.NftCollectionMessage
	5,  // 27: dapplink.account.WalletAccountService.getSupportChains:input_type -> dapplink.account.SupportChainsRequest
	7,  // 28: dapplink.account.WalletAccountService.convertAddress:input_type -> dapplink.account.ConvertAddressRequest
	9,  // 29: dapplink.account.WalletAccountService.validAddress:input_type -> dapplink.account.ValidAddressRequest
	11, // 30: dapplink.account.WalletAccountService.getBlockByNumber:input_type -> dapplink.account.BlockNumberRequest
	12, // 31: dapplink.account.WalletAccountService.getBlockByHash:input_type -> dapplink.account.BlockHashRequest
	15, // 32: dapplink.account.WalletAccountService.getBlockHeaderByHash:input_type -> dapplink.account.BlockHeaderHashRequest
	16, // 33: dapplink.account.WalletAccountService.getBlockHeaderByNumber:input_type -> dapplink.account.BlockHeaderNumberRequest
	18, // 34: dapplink.account.WalletAccountService.getBlockHeaderByRange:input_type -> dapplink.account.BlockByRangeRequest
	20, // 35: dapplink.account.WalletAccountService.getAccount:input_type -> dapplink.account.AccountRequest
	22, // 36: dapplink.account.WalletAccountService.getFee:input_type -> dapplink.account.FeeRequest
	24, // 37: dapplink.account.WalletAccountService.SendTx:input_type -> dapplink.account.SendTxRequest
	26, // 38: dapplink.account.WalletAccountService.getTxByAddress:input_type -> dapplink.account.TxAddressRequest
	28, // 39: dapplink.account.WalletAccountService.getTxByHash:input_type -> dapplink.account.TxHashRequest
	30, // 40: dapplink.account.WalletAccountService.buildUnSignTransaction:input_type -> dapplink.account.UnSignTransactionRequest
	32, // 41: dapplink.account.WalletAccountService.buildSignedTransaction:input_type -> dapplink.account.SignedTransactionRequest
	36, // 42: dapplink.account.WalletAccountService.decodeTransaction:input_type -> dapplink.account.DecodeTransactionRequest
	34, // 43: dapplink.account.WalletAccountService.verifySignedTransaction:input_type -> dapplink.account.VerifyTransactionRequest
	38, // 44: dapplink.account.WalletAccountService.getExtraData:input_type -> dapplink.account.ExtraDataRequest
	41, // 45: dapplink.account.WalletAccountService.getNftListByAddress:input_type -> dapplink.account.NftAddressRequest
	44, // 46: dapplink.account.WalletAccountService.getNftCollection:input_type -> dapplink.account.NftCollectionRequest
	46, // 47: dapplink.account.WalletAccountService.getNftDetail:input_type -> dapplink.account.NftDetailRequest
	48, // 48: dapplink.account.WalletAccountService.getNftHolderList:input_type -> dapplink.account.NftHolderListRequest
	50, // 49: dapplink.account.WalletAccountService.getNftTradeHistory:input_type -> dapplink.account.NftTradeHistoryRequest
	52, // 50: dapplink.account.WalletAccountService.getAddressNftTradeHistory:input_type -> dapplink.account.AddressNftTradeHistoryRequest
	6,  // 51: dapplink.account.WalletAccountService.getSupportChains:output_type -> dapplink.account.SupportChainsResponse
	8,  // 52: dapplink.account.WalletAccountService.convertAddress:output_type -> dapplink.account.ConvertAddressResponse
	10, // 53: dapplink.account.WalletAccountService.validAddress:output_type -> dapplink.account.ValidAddressResponse
	14, // 54: dapplink.account.WalletAccountService.getBlockByNumber:output_type -> dapplink.account.BlockResponse
	14, // 55: dapplink.account.WalletAccountService.getBlockByHash:output_type -> dapplink.account.BlockResponse
	17, // 56: dapplink.account.WalletAccountService.getBlockHeaderByHash:output_type -> dapplink.account.BlockHeaderResponse
	17, // 57: dapplink.account.WalletAccountService.getBlockHeaderByNumber:output_type -> dapplink.account.BlockHeaderResponse
	19, // 58: dapplink.account.WalletAccountService.getBlockHeaderByRange:output_type -> dapplink.account.BlockByRangeResponse
	21, // 59: dapplink.account.WalletAccountService.getAccount:output_type -> dapplink.account.AccountResponse
	23, // 60: dapplink.account.WalletAccountService.getFee:output_type -> dapplink.account.FeeResponse
	25, // 61: dapplink.account.WalletAccountService.SendTx:output_type -> dapplink.account.SendTxResponse
	27, // 62: dapplink.account.WalletAccountService.getTxByAddress:output_type -> dapplink.account.TxAddressResponse
	29, // 63: dapplink.account.WalletAccountService.getTxByHash:output_type -> dapplink.account.TxHashResponse
	31, // 64: dapplink.account.WalletAccountService.buildUnSignTransaction:output_type -> dapplink.account.UnSignTransactionResponse
	33, // 65: dapplink.account.WalletAccountService.buildSignedTransaction:output_type -> dapplink.account.SignedTransactionResponse
	37, // 66: dapplink.account.WalletAccountService.decodeTransaction:output_type -> dapplink.account.DecodeTransactionResponse
	35, // 67: dapplink.account.WalletAccountService.verifySignedTransaction:output_type -> dapplink.account.VerifyTransactionResponse
	39, // 68: dapplink.account.WalletAccountService.getExtraData:output_type -> dapplink.account.ExtraDataResponse
	42, // 69: dapplink.account.WalletAccountService.getNftListByAddress:output_type -> dapplink.account.NftAddressResponse
	45, // 70: dapplink.account.WalletAccountService.getNftCollection:output_type -> dapplink.account.NftCollectionResponse
	47, // 71: dapplink.account.WalletAccountService.getNftDetail:output_type -> dapplink.account.NftDetailResponse
	49, // 72: dapplink.account.WalletAccountService.getNftHolderList:output_type -> dapplink.account.NftHolderListResponse
	51, // 73: dapplink.account.WalletAccountService.getNftTradeHistory:output_type -> dapplink.account.NftTradeHistoryResponse
	53, // 74: dapplink.account.WalletAccountService.getAddressNftTradeHistory:output_type -> dapplink.account.AddressNftTradeHistoryResponse
	51, // [51:75] is the sub-list for method output_type
	27, // [27:51] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_dapplink_account_proto_init() }
func file_dapplink_account_proto_init() {
	if File_dapplink_account_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_dapplink_account_proto_rawDesc), len(file_dapplink_account_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   53,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_dapplink_account_proto_goTypes,
		DependencyIndexes: file_dapplink_account_proto_depIdxs,
		EnumInfos:         file_dapplink_account_proto_enumTypes,
		MessageInfos:      file_dapplink_account_proto_msgTypes,
	}.Build()
	File_dapplink_account_proto = out.File
	file_dapplink_account_proto_goTypes = nil
	file_dapplink_account_proto_depIdxs = nil
}
