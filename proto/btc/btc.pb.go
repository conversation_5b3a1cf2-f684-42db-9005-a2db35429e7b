// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v4.25.3
// source: dapplink/btc.proto

package btc

import (
	common "./proto/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TxStatus int32

const (
	TxStatus_NotFound              TxStatus = 0
	TxStatus_Pending               TxStatus = 1
	TxStatus_Failed                TxStatus = 2
	TxStatus_Success               TxStatus = 3
	TxStatus_ContractExecuteFailed TxStatus = 4
	TxStatus_Other                 TxStatus = 5
)

// Enum value maps for TxStatus.
var (
	TxStatus_name = map[int32]string{
		0: "NotFound",
		1: "Pending",
		2: "Failed",
		3: "Success",
		4: "ContractExecuteFailed",
		5: "Other",
	}
	TxStatus_value = map[string]int32{
		"NotFound":              0,
		"Pending":               1,
		"Failed":                2,
		"Success":               3,
		"ContractExecuteFailed": 4,
		"Other":                 5,
	}
)

func (x TxStatus) Enum() *TxStatus {
	p := new(TxStatus)
	*p = x
	return p
}

func (x TxStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TxStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_dapplink_btc_proto_enumTypes[0].Descriptor()
}

func (TxStatus) Type() protoreflect.EnumType {
	return &file_dapplink_btc_proto_enumTypes[0]
}

func (x TxStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TxStatus.Descriptor instead.
func (TxStatus) EnumDescriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{0}
}

type Vin struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Hash          string                 `protobuf:"bytes,1,opt,name=hash,proto3" json:"hash,omitempty"`
	Index         uint32                 `protobuf:"varint,2,opt,name=index,proto3" json:"index,omitempty"`
	Amount        int64                  `protobuf:"varint,3,opt,name=amount,proto3" json:"amount,omitempty"`
	Address       string                 `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Vin) Reset() {
	*x = Vin{}
	mi := &file_dapplink_btc_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Vin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Vin) ProtoMessage() {}

func (x *Vin) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Vin.ProtoReflect.Descriptor instead.
func (*Vin) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{0}
}

func (x *Vin) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *Vin) GetIndex() uint32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *Vin) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *Vin) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type Vout struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Address       string                 `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	Amount        int64                  `protobuf:"varint,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Index         uint32                 `protobuf:"varint,3,opt,name=index,proto3" json:"index,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Vout) Reset() {
	*x = Vout{}
	mi := &file_dapplink_btc_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Vout) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Vout) ProtoMessage() {}

func (x *Vout) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Vout.ProtoReflect.Descriptor instead.
func (*Vout) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{1}
}

func (x *Vout) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *Vout) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *Vout) GetIndex() uint32 {
	if x != nil {
		return x.Index
	}
	return 0
}

type Address struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Address       string                 `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Address) Reset() {
	*x = Address{}
	mi := &file_dapplink_btc_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Address) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Address) ProtoMessage() {}

func (x *Address) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Address.ProtoReflect.Descriptor instead.
func (*Address) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{2}
}

func (x *Address) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type Value struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Value         string                 `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Value) Reset() {
	*x = Value{}
	mi := &file_dapplink_btc_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Value) ProtoMessage() {}

func (x *Value) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Value.ProtoReflect.Descriptor instead.
func (*Value) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{3}
}

func (x *Value) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type TxMessage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Hash          string                 `protobuf:"bytes,1,opt,name=hash,proto3" json:"hash,omitempty"`
	Index         uint32                 `protobuf:"varint,2,opt,name=index,proto3" json:"index,omitempty"`
	Froms         []*Address             `protobuf:"bytes,3,rep,name=froms,proto3" json:"froms,omitempty"`
	Tos           []*Address             `protobuf:"bytes,4,rep,name=tos,proto3" json:"tos,omitempty"`
	Values        []*Value               `protobuf:"bytes,7,rep,name=values,proto3" json:"values,omitempty"`
	Fee           string                 `protobuf:"bytes,5,opt,name=fee,proto3" json:"fee,omitempty"`
	Status        TxStatus               `protobuf:"varint,6,opt,name=status,proto3,enum=dapplink.btc.TxStatus" json:"status,omitempty"`
	Type          int32                  `protobuf:"varint,8,opt,name=type,proto3" json:"type,omitempty"`
	Height        string                 `protobuf:"bytes,9,opt,name=height,proto3" json:"height,omitempty"`
	Brc20Address  string                 `protobuf:"bytes,10,opt,name=brc20_address,json=brc20Address,proto3" json:"brc20_address,omitempty"`
	Datetime      string                 `protobuf:"bytes,11,opt,name=datetime,proto3" json:"datetime,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TxMessage) Reset() {
	*x = TxMessage{}
	mi := &file_dapplink_btc_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TxMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxMessage) ProtoMessage() {}

func (x *TxMessage) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxMessage.ProtoReflect.Descriptor instead.
func (*TxMessage) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{4}
}

func (x *TxMessage) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *TxMessage) GetIndex() uint32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *TxMessage) GetFroms() []*Address {
	if x != nil {
		return x.Froms
	}
	return nil
}

func (x *TxMessage) GetTos() []*Address {
	if x != nil {
		return x.Tos
	}
	return nil
}

func (x *TxMessage) GetValues() []*Value {
	if x != nil {
		return x.Values
	}
	return nil
}

func (x *TxMessage) GetFee() string {
	if x != nil {
		return x.Fee
	}
	return ""
}

func (x *TxMessage) GetStatus() TxStatus {
	if x != nil {
		return x.Status
	}
	return TxStatus_NotFound
}

func (x *TxMessage) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *TxMessage) GetHeight() string {
	if x != nil {
		return x.Height
	}
	return ""
}

func (x *TxMessage) GetBrc20Address() string {
	if x != nil {
		return x.Brc20Address
	}
	return ""
}

func (x *TxMessage) GetDatetime() string {
	if x != nil {
		return x.Datetime
	}
	return ""
}

type SupportChainsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SupportChainsRequest) Reset() {
	*x = SupportChainsRequest{}
	mi := &file_dapplink_btc_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SupportChainsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SupportChainsRequest) ProtoMessage() {}

func (x *SupportChainsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SupportChainsRequest.ProtoReflect.Descriptor instead.
func (*SupportChainsRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{5}
}

func (x *SupportChainsRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *SupportChainsRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *SupportChainsRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

type SupportChainsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Support       bool                   `protobuf:"varint,3,opt,name=support,proto3" json:"support,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SupportChainsResponse) Reset() {
	*x = SupportChainsResponse{}
	mi := &file_dapplink_btc_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SupportChainsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SupportChainsResponse) ProtoMessage() {}

func (x *SupportChainsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SupportChainsResponse.ProtoReflect.Descriptor instead.
func (*SupportChainsResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{6}
}

func (x *SupportChainsResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *SupportChainsResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SupportChainsResponse) GetSupport() bool {
	if x != nil {
		return x.Support
	}
	return false
}

type ConvertAddressRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
	Format        string                 `protobuf:"bytes,4,opt,name=format,proto3" json:"format,omitempty"`
	PublicKey     string                 `protobuf:"bytes,5,opt,name=public_key,json=publicKey,proto3" json:"public_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConvertAddressRequest) Reset() {
	*x = ConvertAddressRequest{}
	mi := &file_dapplink_btc_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConvertAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConvertAddressRequest) ProtoMessage() {}

func (x *ConvertAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConvertAddressRequest.ProtoReflect.Descriptor instead.
func (*ConvertAddressRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{7}
}

func (x *ConvertAddressRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *ConvertAddressRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *ConvertAddressRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *ConvertAddressRequest) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *ConvertAddressRequest) GetPublicKey() string {
	if x != nil {
		return x.PublicKey
	}
	return ""
}

type ConvertAddressResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Address       string                 `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConvertAddressResponse) Reset() {
	*x = ConvertAddressResponse{}
	mi := &file_dapplink_btc_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConvertAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConvertAddressResponse) ProtoMessage() {}

func (x *ConvertAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConvertAddressResponse.ProtoReflect.Descriptor instead.
func (*ConvertAddressResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{8}
}

func (x *ConvertAddressResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *ConvertAddressResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ConvertAddressResponse) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type ValidAddressRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
	Format        string                 `protobuf:"bytes,4,opt,name=format,proto3" json:"format,omitempty"`
	Address       string                 `protobuf:"bytes,5,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidAddressRequest) Reset() {
	*x = ValidAddressRequest{}
	mi := &file_dapplink_btc_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidAddressRequest) ProtoMessage() {}

func (x *ValidAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidAddressRequest.ProtoReflect.Descriptor instead.
func (*ValidAddressRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{9}
}

func (x *ValidAddressRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *ValidAddressRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *ValidAddressRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *ValidAddressRequest) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *ValidAddressRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type ValidAddressResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Valid         bool                   `protobuf:"varint,3,opt,name=valid,proto3" json:"valid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidAddressResponse) Reset() {
	*x = ValidAddressResponse{}
	mi := &file_dapplink_btc_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidAddressResponse) ProtoMessage() {}

func (x *ValidAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidAddressResponse.ProtoReflect.Descriptor instead.
func (*ValidAddressResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{10}
}

func (x *ValidAddressResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *ValidAddressResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ValidAddressResponse) GetValid() bool {
	if x != nil {
		return x.Valid
	}
	return false
}

type FeeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Coin          string                 `protobuf:"bytes,3,opt,name=coin,proto3" json:"coin,omitempty"`
	Network       string                 `protobuf:"bytes,4,opt,name=network,proto3" json:"network,omitempty"`
	RawTx         string                 `protobuf:"bytes,5,opt,name=rawTx,proto3" json:"rawTx,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FeeRequest) Reset() {
	*x = FeeRequest{}
	mi := &file_dapplink_btc_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FeeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeeRequest) ProtoMessage() {}

func (x *FeeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeeRequest.ProtoReflect.Descriptor instead.
func (*FeeRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{11}
}

func (x *FeeRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *FeeRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *FeeRequest) GetCoin() string {
	if x != nil {
		return x.Coin
	}
	return ""
}

func (x *FeeRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *FeeRequest) GetRawTx() string {
	if x != nil {
		return x.RawTx
	}
	return ""
}

type FeeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	BestFee       string                 `protobuf:"bytes,3,opt,name=best_fee,json=bestFee,proto3" json:"best_fee,omitempty"`
	BestFeeSat    string                 `protobuf:"bytes,4,opt,name=best_fee_sat,json=bestFeeSat,proto3" json:"best_fee_sat,omitempty"`
	SlowFee       string                 `protobuf:"bytes,5,opt,name=slow_fee,json=slowFee,proto3" json:"slow_fee,omitempty"`
	NormalFee     string                 `protobuf:"bytes,6,opt,name=normal_fee,json=normalFee,proto3" json:"normal_fee,omitempty"`
	FastFee       string                 `protobuf:"bytes,7,opt,name=fast_fee,json=fastFee,proto3" json:"fast_fee,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FeeResponse) Reset() {
	*x = FeeResponse{}
	mi := &file_dapplink_btc_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FeeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeeResponse) ProtoMessage() {}

func (x *FeeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeeResponse.ProtoReflect.Descriptor instead.
func (*FeeResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{12}
}

func (x *FeeResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *FeeResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *FeeResponse) GetBestFee() string {
	if x != nil {
		return x.BestFee
	}
	return ""
}

func (x *FeeResponse) GetBestFeeSat() string {
	if x != nil {
		return x.BestFeeSat
	}
	return ""
}

func (x *FeeResponse) GetSlowFee() string {
	if x != nil {
		return x.SlowFee
	}
	return ""
}

func (x *FeeResponse) GetNormalFee() string {
	if x != nil {
		return x.NormalFee
	}
	return ""
}

func (x *FeeResponse) GetFastFee() string {
	if x != nil {
		return x.FastFee
	}
	return ""
}

type AccountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
	Address       string                 `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	Brc20Address  string                 `protobuf:"bytes,5,opt,name=brc20_address,json=brc20Address,proto3" json:"brc20_address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountRequest) Reset() {
	*x = AccountRequest{}
	mi := &file_dapplink_btc_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountRequest) ProtoMessage() {}

func (x *AccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountRequest.ProtoReflect.Descriptor instead.
func (*AccountRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{13}
}

func (x *AccountRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *AccountRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *AccountRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *AccountRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *AccountRequest) GetBrc20Address() string {
	if x != nil {
		return x.Brc20Address
	}
	return ""
}

type AccountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Network       string                 `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
	Balance       string                 `protobuf:"bytes,4,opt,name=balance,proto3" json:"balance,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountResponse) Reset() {
	*x = AccountResponse{}
	mi := &file_dapplink_btc_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountResponse) ProtoMessage() {}

func (x *AccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountResponse.ProtoReflect.Descriptor instead.
func (*AccountResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{14}
}

func (x *AccountResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *AccountResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AccountResponse) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *AccountResponse) GetBalance() string {
	if x != nil {
		return x.Balance
	}
	return ""
}

type UnspentOutput struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	TxId            string                 `protobuf:"bytes,1,opt,name=tx_id,json=txId,proto3" json:"tx_id,omitempty"`
	TxHashBigEndian string                 `protobuf:"bytes,2,opt,name=tx_hash_big_endian,json=txHashBigEndian,proto3" json:"tx_hash_big_endian,omitempty"`
	TxOutputN       uint64                 `protobuf:"varint,3,opt,name=tx_output_n,json=txOutputN,proto3" json:"tx_output_n,omitempty"`
	Script          string                 `protobuf:"bytes,4,opt,name=script,proto3" json:"script,omitempty"`
	Height          string                 `protobuf:"bytes,5,opt,name=height,proto3" json:"height,omitempty"`
	BlockTime       string                 `protobuf:"bytes,6,opt,name=block_time,json=blockTime,proto3" json:"block_time,omitempty"`
	Address         string                 `protobuf:"bytes,7,opt,name=address,proto3" json:"address,omitempty"`
	UnspentAmount   string                 `protobuf:"bytes,8,opt,name=unspent_amount,json=unspentAmount,proto3" json:"unspent_amount,omitempty"`
	ValueHex        string                 `protobuf:"bytes,9,opt,name=value_hex,json=valueHex,proto3" json:"value_hex,omitempty"`
	Confirmations   uint64                 `protobuf:"varint,10,opt,name=confirmations,proto3" json:"confirmations,omitempty"`
	Index           uint64                 `protobuf:"varint,11,opt,name=index,proto3" json:"index,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *UnspentOutput) Reset() {
	*x = UnspentOutput{}
	mi := &file_dapplink_btc_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnspentOutput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnspentOutput) ProtoMessage() {}

func (x *UnspentOutput) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnspentOutput.ProtoReflect.Descriptor instead.
func (*UnspentOutput) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{15}
}

func (x *UnspentOutput) GetTxId() string {
	if x != nil {
		return x.TxId
	}
	return ""
}

func (x *UnspentOutput) GetTxHashBigEndian() string {
	if x != nil {
		return x.TxHashBigEndian
	}
	return ""
}

func (x *UnspentOutput) GetTxOutputN() uint64 {
	if x != nil {
		return x.TxOutputN
	}
	return 0
}

func (x *UnspentOutput) GetScript() string {
	if x != nil {
		return x.Script
	}
	return ""
}

func (x *UnspentOutput) GetHeight() string {
	if x != nil {
		return x.Height
	}
	return ""
}

func (x *UnspentOutput) GetBlockTime() string {
	if x != nil {
		return x.BlockTime
	}
	return ""
}

func (x *UnspentOutput) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *UnspentOutput) GetUnspentAmount() string {
	if x != nil {
		return x.UnspentAmount
	}
	return ""
}

func (x *UnspentOutput) GetValueHex() string {
	if x != nil {
		return x.ValueHex
	}
	return ""
}

func (x *UnspentOutput) GetConfirmations() uint64 {
	if x != nil {
		return x.Confirmations
	}
	return 0
}

func (x *UnspentOutput) GetIndex() uint64 {
	if x != nil {
		return x.Index
	}
	return 0
}

type UnspentOutputsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
	Address       string                 `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnspentOutputsRequest) Reset() {
	*x = UnspentOutputsRequest{}
	mi := &file_dapplink_btc_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnspentOutputsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnspentOutputsRequest) ProtoMessage() {}

func (x *UnspentOutputsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnspentOutputsRequest.ProtoReflect.Descriptor instead.
func (*UnspentOutputsRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{16}
}

func (x *UnspentOutputsRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *UnspentOutputsRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *UnspentOutputsRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *UnspentOutputsRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type UnspentOutputsResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Code           common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg            string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	UnspentOutputs []*UnspentOutput       `protobuf:"bytes,3,rep,name=unspent_outputs,json=unspentOutputs,proto3" json:"unspent_outputs,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UnspentOutputsResponse) Reset() {
	*x = UnspentOutputsResponse{}
	mi := &file_dapplink_btc_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnspentOutputsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnspentOutputsResponse) ProtoMessage() {}

func (x *UnspentOutputsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnspentOutputsResponse.ProtoReflect.Descriptor instead.
func (*UnspentOutputsResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{17}
}

func (x *UnspentOutputsResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *UnspentOutputsResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UnspentOutputsResponse) GetUnspentOutputs() []*UnspentOutput {
	if x != nil {
		return x.UnspentOutputs
	}
	return nil
}

type BlockNumberRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Height        int64                  `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockNumberRequest) Reset() {
	*x = BlockNumberRequest{}
	mi := &file_dapplink_btc_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockNumberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockNumberRequest) ProtoMessage() {}

func (x *BlockNumberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockNumberRequest.ProtoReflect.Descriptor instead.
func (*BlockNumberRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{18}
}

func (x *BlockNumberRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *BlockNumberRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *BlockNumberRequest) GetHeight() int64 {
	if x != nil {
		return x.Height
	}
	return 0
}

type BlockHashRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Hash          string                 `protobuf:"bytes,3,opt,name=hash,proto3" json:"hash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockHashRequest) Reset() {
	*x = BlockHashRequest{}
	mi := &file_dapplink_btc_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockHashRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockHashRequest) ProtoMessage() {}

func (x *BlockHashRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockHashRequest.ProtoReflect.Descriptor instead.
func (*BlockHashRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{19}
}

func (x *BlockHashRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *BlockHashRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *BlockHashRequest) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

type TransactionList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Hash          string                 `protobuf:"bytes,1,opt,name=hash,proto3" json:"hash,omitempty"`
	Fee           string                 `protobuf:"bytes,2,opt,name=fee,proto3" json:"fee,omitempty"`
	Vin           []*Vin                 `protobuf:"bytes,3,rep,name=vin,proto3" json:"vin,omitempty"`
	Vout          []*Vout                `protobuf:"bytes,4,rep,name=vout,proto3" json:"vout,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TransactionList) Reset() {
	*x = TransactionList{}
	mi := &file_dapplink_btc_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionList) ProtoMessage() {}

func (x *TransactionList) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionList.ProtoReflect.Descriptor instead.
func (*TransactionList) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{20}
}

func (x *TransactionList) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *TransactionList) GetFee() string {
	if x != nil {
		return x.Fee
	}
	return ""
}

func (x *TransactionList) GetVin() []*Vin {
	if x != nil {
		return x.Vin
	}
	return nil
}

func (x *TransactionList) GetVout() []*Vout {
	if x != nil {
		return x.Vout
	}
	return nil
}

type BlockResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Height        uint64                 `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
	Hash          string                 `protobuf:"bytes,4,opt,name=hash,proto3" json:"hash,omitempty"`
	TxList        []*TransactionList     `protobuf:"bytes,5,rep,name=tx_list,json=txList,proto3" json:"tx_list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockResponse) Reset() {
	*x = BlockResponse{}
	mi := &file_dapplink_btc_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockResponse) ProtoMessage() {}

func (x *BlockResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockResponse.ProtoReflect.Descriptor instead.
func (*BlockResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{21}
}

func (x *BlockResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *BlockResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *BlockResponse) GetHeight() uint64 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *BlockResponse) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *BlockResponse) GetTxList() []*TransactionList {
	if x != nil {
		return x.TxList
	}
	return nil
}

type BlockHeaderHashRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Chain         string                 `protobuf:"bytes,1,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,2,opt,name=network,proto3" json:"network,omitempty"`
	Hash          string                 `protobuf:"bytes,3,opt,name=hash,proto3" json:"hash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockHeaderHashRequest) Reset() {
	*x = BlockHeaderHashRequest{}
	mi := &file_dapplink_btc_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockHeaderHashRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockHeaderHashRequest) ProtoMessage() {}

func (x *BlockHeaderHashRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockHeaderHashRequest.ProtoReflect.Descriptor instead.
func (*BlockHeaderHashRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{22}
}

func (x *BlockHeaderHashRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *BlockHeaderHashRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *BlockHeaderHashRequest) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

type BlockHeaderNumberRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Chain         string                 `protobuf:"bytes,1,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,2,opt,name=network,proto3" json:"network,omitempty"`
	Height        int64                  `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockHeaderNumberRequest) Reset() {
	*x = BlockHeaderNumberRequest{}
	mi := &file_dapplink_btc_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockHeaderNumberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockHeaderNumberRequest) ProtoMessage() {}

func (x *BlockHeaderNumberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockHeaderNumberRequest.ProtoReflect.Descriptor instead.
func (*BlockHeaderNumberRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{23}
}

func (x *BlockHeaderNumberRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *BlockHeaderNumberRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *BlockHeaderNumberRequest) GetHeight() int64 {
	if x != nil {
		return x.Height
	}
	return 0
}

type BlockHeaderResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	ParentHash    string                 `protobuf:"bytes,3,opt,name=parent_hash,json=parentHash,proto3" json:"parent_hash,omitempty"`
	BlockHash     string                 `protobuf:"bytes,4,opt,name=block_hash,json=blockHash,proto3" json:"block_hash,omitempty"`
	MerkleRoot    string                 `protobuf:"bytes,5,opt,name=merkle_root,json=merkleRoot,proto3" json:"merkle_root,omitempty"`
	Number        string                 `protobuf:"bytes,6,opt,name=number,proto3" json:"number,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockHeaderResponse) Reset() {
	*x = BlockHeaderResponse{}
	mi := &file_dapplink_btc_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockHeaderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockHeaderResponse) ProtoMessage() {}

func (x *BlockHeaderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockHeaderResponse.ProtoReflect.Descriptor instead.
func (*BlockHeaderResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{24}
}

func (x *BlockHeaderResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *BlockHeaderResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *BlockHeaderResponse) GetParentHash() string {
	if x != nil {
		return x.ParentHash
	}
	return ""
}

func (x *BlockHeaderResponse) GetBlockHash() string {
	if x != nil {
		return x.BlockHash
	}
	return ""
}

func (x *BlockHeaderResponse) GetMerkleRoot() string {
	if x != nil {
		return x.MerkleRoot
	}
	return ""
}

func (x *BlockHeaderResponse) GetNumber() string {
	if x != nil {
		return x.Number
	}
	return ""
}

type SendTxRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Coin          string                 `protobuf:"bytes,3,opt,name=coin,proto3" json:"coin,omitempty"`
	Network       string                 `protobuf:"bytes,4,opt,name=network,proto3" json:"network,omitempty"`
	RawTx         string                 `protobuf:"bytes,5,opt,name=raw_tx,json=rawTx,proto3" json:"raw_tx,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendTxRequest) Reset() {
	*x = SendTxRequest{}
	mi := &file_dapplink_btc_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendTxRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendTxRequest) ProtoMessage() {}

func (x *SendTxRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendTxRequest.ProtoReflect.Descriptor instead.
func (*SendTxRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{25}
}

func (x *SendTxRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *SendTxRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *SendTxRequest) GetCoin() string {
	if x != nil {
		return x.Coin
	}
	return ""
}

func (x *SendTxRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *SendTxRequest) GetRawTx() string {
	if x != nil {
		return x.RawTx
	}
	return ""
}

type SendTxResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	TxHash        string                 `protobuf:"bytes,3,opt,name=tx_hash,json=txHash,proto3" json:"tx_hash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendTxResponse) Reset() {
	*x = SendTxResponse{}
	mi := &file_dapplink_btc_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendTxResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendTxResponse) ProtoMessage() {}

func (x *SendTxResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendTxResponse.ProtoReflect.Descriptor instead.
func (*SendTxResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{26}
}

func (x *SendTxResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *SendTxResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SendTxResponse) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

type TxAddressRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Coin          string                 `protobuf:"bytes,3,opt,name=coin,proto3" json:"coin,omitempty"`
	Network       string                 `protobuf:"bytes,4,opt,name=network,proto3" json:"network,omitempty"`
	Address       string                 `protobuf:"bytes,5,opt,name=address,proto3" json:"address,omitempty"`
	Brc20Address  string                 `protobuf:"bytes,6,opt,name=brc20_address,json=brc20Address,proto3" json:"brc20_address,omitempty"`
	Page          uint32                 `protobuf:"varint,7,opt,name=page,proto3" json:"page,omitempty"`
	Pagesize      uint32                 `protobuf:"varint,8,opt,name=pagesize,proto3" json:"pagesize,omitempty"`
	Cursor        string                 `protobuf:"bytes,9,opt,name=cursor,proto3" json:"cursor,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TxAddressRequest) Reset() {
	*x = TxAddressRequest{}
	mi := &file_dapplink_btc_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TxAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxAddressRequest) ProtoMessage() {}

func (x *TxAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxAddressRequest.ProtoReflect.Descriptor instead.
func (*TxAddressRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{27}
}

func (x *TxAddressRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *TxAddressRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *TxAddressRequest) GetCoin() string {
	if x != nil {
		return x.Coin
	}
	return ""
}

func (x *TxAddressRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *TxAddressRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *TxAddressRequest) GetBrc20Address() string {
	if x != nil {
		return x.Brc20Address
	}
	return ""
}

func (x *TxAddressRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *TxAddressRequest) GetPagesize() uint32 {
	if x != nil {
		return x.Pagesize
	}
	return 0
}

func (x *TxAddressRequest) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

type TxAddressResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Tx            []*TxMessage           `protobuf:"bytes,3,rep,name=tx,proto3" json:"tx,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TxAddressResponse) Reset() {
	*x = TxAddressResponse{}
	mi := &file_dapplink_btc_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TxAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxAddressResponse) ProtoMessage() {}

func (x *TxAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxAddressResponse.ProtoReflect.Descriptor instead.
func (*TxAddressResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{28}
}

func (x *TxAddressResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *TxAddressResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *TxAddressResponse) GetTx() []*TxMessage {
	if x != nil {
		return x.Tx
	}
	return nil
}

type TxHashRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Coin          string                 `protobuf:"bytes,3,opt,name=coin,proto3" json:"coin,omitempty"`
	Network       string                 `protobuf:"bytes,4,opt,name=network,proto3" json:"network,omitempty"`
	Hash          string                 `protobuf:"bytes,5,opt,name=hash,proto3" json:"hash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TxHashRequest) Reset() {
	*x = TxHashRequest{}
	mi := &file_dapplink_btc_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TxHashRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxHashRequest) ProtoMessage() {}

func (x *TxHashRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxHashRequest.ProtoReflect.Descriptor instead.
func (*TxHashRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{29}
}

func (x *TxHashRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *TxHashRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *TxHashRequest) GetCoin() string {
	if x != nil {
		return x.Coin
	}
	return ""
}

func (x *TxHashRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *TxHashRequest) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

type TxHashResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Tx            *TxMessage             `protobuf:"bytes,3,opt,name=tx,proto3" json:"tx,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TxHashResponse) Reset() {
	*x = TxHashResponse{}
	mi := &file_dapplink_btc_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TxHashResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxHashResponse) ProtoMessage() {}

func (x *TxHashResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxHashResponse.ProtoReflect.Descriptor instead.
func (*TxHashResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{30}
}

func (x *TxHashResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *TxHashResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *TxHashResponse) GetTx() *TxMessage {
	if x != nil {
		return x.Tx
	}
	return nil
}

type UnSignTransactionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
	Fee           string                 `protobuf:"bytes,4,opt,name=fee,proto3" json:"fee,omitempty"`
	Vin           []*Vin                 `protobuf:"bytes,5,rep,name=vin,proto3" json:"vin,omitempty"`
	Vout          []*Vout                `protobuf:"bytes,6,rep,name=vout,proto3" json:"vout,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnSignTransactionRequest) Reset() {
	*x = UnSignTransactionRequest{}
	mi := &file_dapplink_btc_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnSignTransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnSignTransactionRequest) ProtoMessage() {}

func (x *UnSignTransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnSignTransactionRequest.ProtoReflect.Descriptor instead.
func (*UnSignTransactionRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{31}
}

func (x *UnSignTransactionRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *UnSignTransactionRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *UnSignTransactionRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *UnSignTransactionRequest) GetFee() string {
	if x != nil {
		return x.Fee
	}
	return ""
}

func (x *UnSignTransactionRequest) GetVin() []*Vin {
	if x != nil {
		return x.Vin
	}
	return nil
}

func (x *UnSignTransactionRequest) GetVout() []*Vout {
	if x != nil {
		return x.Vout
	}
	return nil
}

type UnSignTransactionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	TxData        []byte                 `protobuf:"bytes,3,opt,name=tx_data,json=txData,proto3" json:"tx_data,omitempty"`
	SignHashes    [][]byte               `protobuf:"bytes,4,rep,name=sign_hashes,json=signHashes,proto3" json:"sign_hashes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnSignTransactionResponse) Reset() {
	*x = UnSignTransactionResponse{}
	mi := &file_dapplink_btc_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnSignTransactionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnSignTransactionResponse) ProtoMessage() {}

func (x *UnSignTransactionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnSignTransactionResponse.ProtoReflect.Descriptor instead.
func (*UnSignTransactionResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{32}
}

func (x *UnSignTransactionResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *UnSignTransactionResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UnSignTransactionResponse) GetTxData() []byte {
	if x != nil {
		return x.TxData
	}
	return nil
}

func (x *UnSignTransactionResponse) GetSignHashes() [][]byte {
	if x != nil {
		return x.SignHashes
	}
	return nil
}

type SignedTransactionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
	TxData        []byte                 `protobuf:"bytes,4,opt,name=tx_data,json=txData,proto3" json:"tx_data,omitempty"`
	Signatures    [][]byte               `protobuf:"bytes,5,rep,name=signatures,proto3" json:"signatures,omitempty"`
	PublicKeys    [][]byte               `protobuf:"bytes,6,rep,name=public_keys,json=publicKeys,proto3" json:"public_keys,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignedTransactionRequest) Reset() {
	*x = SignedTransactionRequest{}
	mi := &file_dapplink_btc_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignedTransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignedTransactionRequest) ProtoMessage() {}

func (x *SignedTransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignedTransactionRequest.ProtoReflect.Descriptor instead.
func (*SignedTransactionRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{33}
}

func (x *SignedTransactionRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *SignedTransactionRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *SignedTransactionRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *SignedTransactionRequest) GetTxData() []byte {
	if x != nil {
		return x.TxData
	}
	return nil
}

func (x *SignedTransactionRequest) GetSignatures() [][]byte {
	if x != nil {
		return x.Signatures
	}
	return nil
}

func (x *SignedTransactionRequest) GetPublicKeys() [][]byte {
	if x != nil {
		return x.PublicKeys
	}
	return nil
}

type SignedTransactionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	SignedTxData  []byte                 `protobuf:"bytes,3,opt,name=signed_tx_data,json=signedTxData,proto3" json:"signed_tx_data,omitempty"`
	Hash          []byte                 `protobuf:"bytes,4,opt,name=hash,proto3" json:"hash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignedTransactionResponse) Reset() {
	*x = SignedTransactionResponse{}
	mi := &file_dapplink_btc_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignedTransactionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignedTransactionResponse) ProtoMessage() {}

func (x *SignedTransactionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignedTransactionResponse.ProtoReflect.Descriptor instead.
func (*SignedTransactionResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{34}
}

func (x *SignedTransactionResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *SignedTransactionResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SignedTransactionResponse) GetSignedTxData() []byte {
	if x != nil {
		return x.SignedTxData
	}
	return nil
}

func (x *SignedTransactionResponse) GetHash() []byte {
	if x != nil {
		return x.Hash
	}
	return nil
}

type VerifyTransactionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Chain         string                 `protobuf:"bytes,1,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,2,opt,name=network,proto3" json:"network,omitempty"`
	PublicKey     string                 `protobuf:"bytes,3,opt,name=public_key,json=publicKey,proto3" json:"public_key,omitempty"`
	Signature     string                 `protobuf:"bytes,4,opt,name=signature,proto3" json:"signature,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifyTransactionRequest) Reset() {
	*x = VerifyTransactionRequest{}
	mi := &file_dapplink_btc_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyTransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyTransactionRequest) ProtoMessage() {}

func (x *VerifyTransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyTransactionRequest.ProtoReflect.Descriptor instead.
func (*VerifyTransactionRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{35}
}

func (x *VerifyTransactionRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *VerifyTransactionRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *VerifyTransactionRequest) GetPublicKey() string {
	if x != nil {
		return x.PublicKey
	}
	return ""
}

func (x *VerifyTransactionRequest) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

type VerifyTransactionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Verify        bool                   `protobuf:"varint,3,opt,name=verify,proto3" json:"verify,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifyTransactionResponse) Reset() {
	*x = VerifyTransactionResponse{}
	mi := &file_dapplink_btc_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyTransactionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyTransactionResponse) ProtoMessage() {}

func (x *VerifyTransactionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyTransactionResponse.ProtoReflect.Descriptor instead.
func (*VerifyTransactionResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{36}
}

func (x *VerifyTransactionResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *VerifyTransactionResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *VerifyTransactionResponse) GetVerify() bool {
	if x != nil {
		return x.Verify
	}
	return false
}

type DecodeTransactionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Chain         string                 `protobuf:"bytes,1,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,2,opt,name=network,proto3" json:"network,omitempty"`
	RawData       []byte                 `protobuf:"bytes,5,opt,name=raw_data,json=rawData,proto3" json:"raw_data,omitempty"`
	Vins          []*Vin                 `protobuf:"bytes,7,rep,name=vins,proto3" json:"vins,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DecodeTransactionRequest) Reset() {
	*x = DecodeTransactionRequest{}
	mi := &file_dapplink_btc_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DecodeTransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DecodeTransactionRequest) ProtoMessage() {}

func (x *DecodeTransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DecodeTransactionRequest.ProtoReflect.Descriptor instead.
func (*DecodeTransactionRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{37}
}

func (x *DecodeTransactionRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *DecodeTransactionRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *DecodeTransactionRequest) GetRawData() []byte {
	if x != nil {
		return x.RawData
	}
	return nil
}

func (x *DecodeTransactionRequest) GetVins() []*Vin {
	if x != nil {
		return x.Vins
	}
	return nil
}

type DecodeTransactionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	TxHash        string                 `protobuf:"bytes,3,opt,name=tx_hash,json=txHash,proto3" json:"tx_hash,omitempty"`
	Status        TxStatus               `protobuf:"varint,4,opt,name=status,proto3,enum=dapplink.btc.TxStatus" json:"status,omitempty"`
	Vins          []*Vin                 `protobuf:"bytes,5,rep,name=vins,proto3" json:"vins,omitempty"`
	Vouts         []*Vout                `protobuf:"bytes,6,rep,name=vouts,proto3" json:"vouts,omitempty"`
	SignHashes    [][]byte               `protobuf:"bytes,7,rep,name=sign_hashes,json=signHashes,proto3" json:"sign_hashes,omitempty"`
	CostFee       string                 `protobuf:"bytes,8,opt,name=cost_fee,json=costFee,proto3" json:"cost_fee,omitempty"`
	BlockHeight   uint64                 `protobuf:"varint,9,opt,name=block_height,json=blockHeight,proto3" json:"block_height,omitempty"`
	BlockTime     uint64                 `protobuf:"varint,10,opt,name=block_time,json=blockTime,proto3" json:"block_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DecodeTransactionResponse) Reset() {
	*x = DecodeTransactionResponse{}
	mi := &file_dapplink_btc_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DecodeTransactionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DecodeTransactionResponse) ProtoMessage() {}

func (x *DecodeTransactionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_btc_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DecodeTransactionResponse.ProtoReflect.Descriptor instead.
func (*DecodeTransactionResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_btc_proto_rawDescGZIP(), []int{38}
}

func (x *DecodeTransactionResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *DecodeTransactionResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *DecodeTransactionResponse) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

func (x *DecodeTransactionResponse) GetStatus() TxStatus {
	if x != nil {
		return x.Status
	}
	return TxStatus_NotFound
}

func (x *DecodeTransactionResponse) GetVins() []*Vin {
	if x != nil {
		return x.Vins
	}
	return nil
}

func (x *DecodeTransactionResponse) GetVouts() []*Vout {
	if x != nil {
		return x.Vouts
	}
	return nil
}

func (x *DecodeTransactionResponse) GetSignHashes() [][]byte {
	if x != nil {
		return x.SignHashes
	}
	return nil
}

func (x *DecodeTransactionResponse) GetCostFee() string {
	if x != nil {
		return x.CostFee
	}
	return ""
}

func (x *DecodeTransactionResponse) GetBlockHeight() uint64 {
	if x != nil {
		return x.BlockHeight
	}
	return 0
}

func (x *DecodeTransactionResponse) GetBlockTime() uint64 {
	if x != nil {
		return x.BlockTime
	}
	return 0
}

var File_dapplink_btc_proto protoreflect.FileDescriptor

var file_dapplink_btc_proto_rawDesc = string([]byte{
	0x0a, 0x12, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x62, 0x74, 0x63, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62,
	0x74, 0x63, 0x1a, 0x15, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x61, 0x0a, 0x03, 0x56, 0x69, 0x6e,
	0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x68, 0x61, 0x73, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x4e, 0x0a, 0x04,
	0x56, 0x6f, 0x75, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x16,
	0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x23, 0x0a, 0x07,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x22, 0x1d, 0x0a, 0x05, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x22, 0xe7, 0x02, 0x0a, 0x09, 0x54, 0x78, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61,
	0x73, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x2b, 0x0a, 0x05, 0x66, 0x72, 0x6f, 0x6d,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x05,
	0x66, 0x72, 0x6f, 0x6d, 0x73, 0x12, 0x27, 0x0a, 0x03, 0x74, 0x6f, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74,
	0x63, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x03, 0x74, 0x6f, 0x73, 0x12, 0x2b,
	0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x66,
	0x65, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x66, 0x65, 0x65, 0x12, 0x2e, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e,
	0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x54, 0x78, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x72, 0x63,
	0x32, 0x30, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x62, 0x72, 0x63, 0x32, 0x30, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1a,
	0x0a, 0x08, 0x64, 0x61, 0x74, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x6d, 0x0a, 0x14, 0x53, 0x75,
	0x70, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12,
	0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x22, 0x6d, 0x0a, 0x15, 0x53, 0x75, 0x70,
	0x70, 0x6f, 0x72, 0x74, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75,
	0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x22, 0xa5, 0x01, 0x0a, 0x15, 0x43, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12,
	0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x6b, 0x65, 0x79, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b, 0x65, 0x79,
	0x22, 0x6e, 0x0a, 0x16, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x22, 0x9e, 0x01, 0x0a, 0x13, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12,
	0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12,
	0x16, 0x0a, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x22, 0x68, 0x0a, 0x14, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x22, 0x8d, 0x01, 0x0a, 0x0a,
	0x46, 0x65, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x69, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x61, 0x77, 0x54, 0x78, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x61, 0x77, 0x54, 0x78, 0x22, 0xdb, 0x01, 0x0a, 0x0b,
	0x46, 0x65, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x65, 0x73, 0x74, 0x5f,
	0x66, 0x65, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x65, 0x73, 0x74, 0x46,
	0x65, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x62, 0x65, 0x73, 0x74, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x73,
	0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x65, 0x73, 0x74, 0x46, 0x65,
	0x65, 0x53, 0x61, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x6c, 0x6f, 0x77, 0x5f, 0x66, 0x65, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x6c, 0x6f, 0x77, 0x46, 0x65, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x46, 0x65, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x66, 0x61, 0x73, 0x74, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x66, 0x61, 0x73, 0x74, 0x46, 0x65, 0x65, 0x22, 0xa6, 0x01, 0x0a, 0x0e, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e,
	0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x23, 0x0a,
	0x0d, 0x62, 0x72, 0x63, 0x32, 0x30, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x72, 0x63, 0x32, 0x30, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x22, 0x81, 0x01, 0x0a, 0x0f, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d,
	0x73, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x18, 0x0a, 0x07,
	0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x22, 0xda, 0x02, 0x0a, 0x0d, 0x55, 0x6e, 0x73, 0x70, 0x65,
	0x6e, 0x74, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x13, 0x0a, 0x05, 0x74, 0x78, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x78, 0x49, 0x64, 0x12, 0x2b, 0x0a,
	0x12, 0x74, 0x78, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x5f, 0x62, 0x69, 0x67, 0x5f, 0x65, 0x6e, 0x64,
	0x69, 0x61, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x74, 0x78, 0x48, 0x61, 0x73,
	0x68, 0x42, 0x69, 0x67, 0x45, 0x6e, 0x64, 0x69, 0x61, 0x6e, 0x12, 0x1e, 0x0a, 0x0b, 0x74, 0x78,
	0x5f, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x09, 0x74, 0x78, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x4e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x6e, 0x74, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x75, 0x6e, 0x73,
	0x70, 0x65, 0x6e, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x5f, 0x68, 0x65, 0x78, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x48, 0x65, 0x78, 0x12, 0x24, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x14, 0x0a,
	0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x69, 0x6e,
	0x64, 0x65, 0x78, 0x22, 0x88, 0x01, 0x0a, 0x15, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x6e, 0x74, 0x4f,
	0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a,
	0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x9a,
	0x01, 0x0a, 0x16, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x6e, 0x74, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x44, 0x0a, 0x0f, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x6e, 0x74,
	0x5f, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x55, 0x6e,
	0x73, 0x70, 0x65, 0x6e, 0x74, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x0e, 0x75, 0x6e, 0x73,
	0x70, 0x65, 0x6e, 0x74, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x22, 0x69, 0x0a, 0x12, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x16,
	0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0x63, 0x0a, 0x10, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48,
	0x61, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x22, 0x84, 0x01, 0x0a, 0x0f,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68,
	0x61, 0x73, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x66, 0x65, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x66, 0x65, 0x65, 0x12, 0x23, 0x0a, 0x03, 0x76, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74,
	0x63, 0x2e, 0x56, 0x69, 0x6e, 0x52, 0x03, 0x76, 0x69, 0x6e, 0x12, 0x26, 0x0a, 0x04, 0x76, 0x6f,
	0x75, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x56, 0x6f, 0x75, 0x74, 0x52, 0x04, 0x76, 0x6f,
	0x75, 0x74, 0x22, 0xaf, 0x01, 0x0a, 0x0d, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65,
	0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67,
	0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x12, 0x36, 0x0a, 0x07,
	0x74, 0x78, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x06, 0x74, 0x78,
	0x4c, 0x69, 0x73, 0x74, 0x22, 0x5c, 0x0a, 0x16, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x12,
	0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61,
	0x73, 0x68, 0x22, 0x62, 0x0a, 0x18, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x16,
	0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0xca, 0x01, 0x0a, 0x13, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f,
	0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x48, 0x61, 0x73, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x61, 0x73, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65,
	0x72, 0x6b, 0x6c, 0x65, 0x5f, 0x72, 0x6f, 0x6f, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x6d, 0x65, 0x72, 0x6b, 0x6c, 0x65, 0x52, 0x6f, 0x6f, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x22, 0x91, 0x01, 0x0a, 0x0d, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x78, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x63, 0x6f, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x12, 0x15, 0x0a, 0x06, 0x72, 0x61, 0x77, 0x5f, 0x74, 0x78, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x72, 0x61, 0x77, 0x54, 0x78, 0x22, 0x65, 0x0a, 0x0e, 0x53, 0x65, 0x6e, 0x64, 0x54,
	0x78, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x78, 0x5f, 0x68, 0x61, 0x73, 0x68,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x78, 0x48, 0x61, 0x73, 0x68, 0x22, 0x84,
	0x02, 0x0a, 0x10, 0x54, 0x78, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68,
	0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x63, 0x6f, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x18,
	0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x72, 0x63, 0x32,
	0x30, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x62, 0x72, 0x63, 0x32, 0x30, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63,
	0x75, 0x72, 0x73, 0x6f, 0x72, 0x22, 0x78, 0x0a, 0x11, 0x54, 0x78, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x27, 0x0a, 0x02, 0x74, 0x78, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74,
	0x63, 0x2e, 0x54, 0x78, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x02, 0x74, 0x78, 0x22,
	0x8e, 0x01, 0x0a, 0x0d, 0x54, 0x78, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f,
	0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x12, 0x0a, 0x04,
	0x68, 0x61, 0x73, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68,
	0x22, 0x75, 0x0a, 0x0e, 0x54, 0x78, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75,
	0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x27,
	0x0a, 0x02, 0x74, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x64, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x54, 0x78, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x52, 0x02, 0x74, 0x78, 0x22, 0xd0, 0x01, 0x0a, 0x18, 0x55, 0x6e, 0x53, 0x69,
	0x67, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x10, 0x0a, 0x03, 0x66,
	0x65, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x66, 0x65, 0x65, 0x12, 0x23, 0x0a,
	0x03, 0x76, 0x69, 0x6e, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x64, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x56, 0x69, 0x6e, 0x52, 0x03, 0x76,
	0x69, 0x6e, 0x12, 0x26, 0x0a, 0x04, 0x76, 0x6f, 0x75, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e,
	0x56, 0x6f, 0x75, 0x74, 0x52, 0x04, 0x76, 0x6f, 0x75, 0x74, 0x22, 0x91, 0x01, 0x0a, 0x19, 0x55,
	0x6e, 0x53, 0x69, 0x67, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x78, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x74, 0x78, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x0a,
	0x0b, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0c, 0x52, 0x0a, 0x73, 0x69, 0x67, 0x6e, 0x48, 0x61, 0x73, 0x68, 0x65, 0x73, 0x22, 0xcb,
	0x01, 0x0a, 0x18, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x78, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x06, 0x74, 0x78, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x0a, 0x0a, 0x73,
	0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0c, 0x52,
	0x0a, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x70,
	0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0c,
	0x52, 0x0a, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b, 0x65, 0x79, 0x73, 0x22, 0x91, 0x01, 0x0a,
	0x19, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64,
	0x5f, 0x74, 0x78, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c,
	0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x78, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04,
	0x68, 0x61, 0x73, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68,
	0x22, 0x87, 0x01, 0x0a, 0x18, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a,
	0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68,
	0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b, 0x65, 0x79, 0x12, 0x1c, 0x0a, 0x09,
	0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x22, 0x6f, 0x0a, 0x19, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x06, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x22, 0x8c, 0x01, 0x0a, 0x18,
	0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x18,
	0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x61, 0x77, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x72, 0x61, 0x77, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x25, 0x0a, 0x04, 0x76, 0x69, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63,
	0x2e, 0x56, 0x69, 0x6e, 0x52, 0x04, 0x76, 0x69, 0x6e, 0x73, 0x22, 0xef, 0x02, 0x0a, 0x19, 0x44,
	0x65, 0x63, 0x6f, 0x64, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x78, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x78, 0x48, 0x61, 0x73, 0x68, 0x12, 0x2e, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e,
	0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x54, 0x78, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a,
	0x04, 0x76, 0x69, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x64, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x56, 0x69, 0x6e, 0x52, 0x04,
	0x76, 0x69, 0x6e, 0x73, 0x12, 0x28, 0x0a, 0x05, 0x76, 0x6f, 0x75, 0x74, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62,
	0x74, 0x63, 0x2e, 0x56, 0x6f, 0x75, 0x74, 0x52, 0x05, 0x76, 0x6f, 0x75, 0x74, 0x73, 0x12, 0x1f,
	0x0a, 0x0b, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x65, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x0c, 0x52, 0x0a, 0x73, 0x69, 0x67, 0x6e, 0x48, 0x61, 0x73, 0x68, 0x65, 0x73, 0x12,
	0x19, 0x0a, 0x08, 0x63, 0x6f, 0x73, 0x74, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6f, 0x73, 0x74, 0x46, 0x65, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x2a, 0x64, 0x0a, 0x08,
	0x54, 0x78, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0c, 0x0a, 0x08, 0x4e, 0x6f, 0x74, 0x46,
	0x6f, 0x75, 0x6e, 0x64, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0x02, 0x12,
	0x0b, 0x0a, 0x07, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x10, 0x03, 0x12, 0x19, 0x0a, 0x15,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x46,
	0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0x04, 0x12, 0x09, 0x0a, 0x05, 0x4f, 0x74, 0x68, 0x65, 0x72,
	0x10, 0x05, 0x32, 0xa2, 0x0c, 0x0a, 0x10, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x42, 0x74, 0x63,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x5d, 0x0a, 0x10, 0x67, 0x65, 0x74, 0x53, 0x75,
	0x70, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x73, 0x12, 0x22, 0x2e, 0x64, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x53, 0x75, 0x70, 0x70, 0x6f,
	0x72, 0x74, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x23, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x53,
	0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5d, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x23, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e,
	0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x43, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x57, 0x0a, 0x0c, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x21, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x62, 0x74, 0x63, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x3f,
	0x0a, 0x06, 0x67, 0x65, 0x74, 0x46, 0x65, 0x65, 0x12, 0x18, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x46, 0x65, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x19, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74,
	0x63, 0x2e, 0x46, 0x65, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x4b, 0x0a, 0x0a, 0x67, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1c, 0x2e,
	0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x64, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x60, 0x0a, 0x11,
	0x67, 0x65, 0x74, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x6e, 0x74, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74,
	0x73, 0x12, 0x23, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63,
	0x2e, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x6e, 0x74, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x6e, 0x74, 0x4f, 0x75, 0x74,
	0x70, 0x75, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x53,
	0x0a, 0x10, 0x67, 0x65, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x42, 0x79, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x20, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74,
	0x63, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x62, 0x74, 0x63, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x4f, 0x0a, 0x0e, 0x67, 0x65, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x42,
	0x79, 0x48, 0x61, 0x73, 0x68, 0x12, 0x1e, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x62, 0x74, 0x63, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x62, 0x74, 0x63, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x61, 0x0a, 0x14, 0x67, 0x65, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x79, 0x48, 0x61, 0x73, 0x68, 0x12, 0x24, 0x2e, 0x64,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x21, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74,
	0x63, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x65, 0x0a, 0x16, 0x67, 0x65, 0x74, 0x42, 0x6c,
	0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x26, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63,
	0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x64, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x45,
	0x0a, 0x06, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x78, 0x12, 0x1b, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x78, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x62, 0x74, 0x63, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x78, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x53, 0x0a, 0x0e, 0x67, 0x65, 0x74, 0x54, 0x78, 0x42, 0x79,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1e, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x54, 0x78, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x54, 0x78, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4a, 0x0a, 0x0b, 0x67, 0x65,
	0x74, 0x54, 0x78, 0x42, 0x79, 0x48, 0x61, 0x73, 0x68, 0x12, 0x1b, 0x2e, 0x64, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x54, 0x78, 0x48, 0x61, 0x73, 0x68, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x54, 0x78, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6c, 0x0a, 0x17, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x55, 0x6e, 0x53, 0x69, 0x67, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x26, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63,
	0x2e, 0x55, 0x6e, 0x53, 0x69, 0x67, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x64, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x55, 0x6e, 0x53, 0x69, 0x67, 0x6e, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x6b, 0x0a, 0x16, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x53, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26,
	0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x53, 0x69,
	0x67, 0x6e, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x66, 0x0a, 0x11, 0x64, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27,
	0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x44, 0x65,
	0x63, 0x6f, 0x64, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6c, 0x0a, 0x17, 0x76, 0x65, 0x72,
	0x69, 0x66, 0x79, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x62, 0x74, 0x63, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x64,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x2e, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x1f, 0x0a, 0x10, 0x78, 0x79, 0x7a, 0x2e, 0x64,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x62, 0x74, 0x63, 0x5a, 0x0b, 0x2e, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x62, 0x74, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_dapplink_btc_proto_rawDescOnce sync.Once
	file_dapplink_btc_proto_rawDescData []byte
)

func file_dapplink_btc_proto_rawDescGZIP() []byte {
	file_dapplink_btc_proto_rawDescOnce.Do(func() {
		file_dapplink_btc_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_dapplink_btc_proto_rawDesc), len(file_dapplink_btc_proto_rawDesc)))
	})
	return file_dapplink_btc_proto_rawDescData
}

var file_dapplink_btc_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_dapplink_btc_proto_msgTypes = make([]protoimpl.MessageInfo, 39)
var file_dapplink_btc_proto_goTypes = []any{
	(TxStatus)(0),                     // 0: dapplink.btc.TxStatus
	(*Vin)(nil),                       // 1: dapplink.btc.Vin
	(*Vout)(nil),                      // 2: dapplink.btc.Vout
	(*Address)(nil),                   // 3: dapplink.btc.Address
	(*Value)(nil),                     // 4: dapplink.btc.Value
	(*TxMessage)(nil),                 // 5: dapplink.btc.TxMessage
	(*SupportChainsRequest)(nil),      // 6: dapplink.btc.SupportChainsRequest
	(*SupportChainsResponse)(nil),     // 7: dapplink.btc.SupportChainsResponse
	(*ConvertAddressRequest)(nil),     // 8: dapplink.btc.ConvertAddressRequest
	(*ConvertAddressResponse)(nil),    // 9: dapplink.btc.ConvertAddressResponse
	(*ValidAddressRequest)(nil),       // 10: dapplink.btc.ValidAddressRequest
	(*ValidAddressResponse)(nil),      // 11: dapplink.btc.ValidAddressResponse
	(*FeeRequest)(nil),                // 12: dapplink.btc.FeeRequest
	(*FeeResponse)(nil),               // 13: dapplink.btc.FeeResponse
	(*AccountRequest)(nil),            // 14: dapplink.btc.AccountRequest
	(*AccountResponse)(nil),           // 15: dapplink.btc.AccountResponse
	(*UnspentOutput)(nil),             // 16: dapplink.btc.UnspentOutput
	(*UnspentOutputsRequest)(nil),     // 17: dapplink.btc.UnspentOutputsRequest
	(*UnspentOutputsResponse)(nil),    // 18: dapplink.btc.UnspentOutputsResponse
	(*BlockNumberRequest)(nil),        // 19: dapplink.btc.BlockNumberRequest
	(*BlockHashRequest)(nil),          // 20: dapplink.btc.BlockHashRequest
	(*TransactionList)(nil),           // 21: dapplink.btc.TransactionList
	(*BlockResponse)(nil),             // 22: dapplink.btc.BlockResponse
	(*BlockHeaderHashRequest)(nil),    // 23: dapplink.btc.BlockHeaderHashRequest
	(*BlockHeaderNumberRequest)(nil),  // 24: dapplink.btc.BlockHeaderNumberRequest
	(*BlockHeaderResponse)(nil),       // 25: dapplink.btc.BlockHeaderResponse
	(*SendTxRequest)(nil),             // 26: dapplink.btc.SendTxRequest
	(*SendTxResponse)(nil),            // 27: dapplink.btc.SendTxResponse
	(*TxAddressRequest)(nil),          // 28: dapplink.btc.TxAddressRequest
	(*TxAddressResponse)(nil),         // 29: dapplink.btc.TxAddressResponse
	(*TxHashRequest)(nil),             // 30: dapplink.btc.TxHashRequest
	(*TxHashResponse)(nil),            // 31: dapplink.btc.TxHashResponse
	(*UnSignTransactionRequest)(nil),  // 32: dapplink.btc.UnSignTransactionRequest
	(*UnSignTransactionResponse)(nil), // 33: dapplink.btc.UnSignTransactionResponse
	(*SignedTransactionRequest)(nil),  // 34: dapplink.btc.SignedTransactionRequest
	(*SignedTransactionResponse)(nil), // 35: dapplink.btc.SignedTransactionResponse
	(*VerifyTransactionRequest)(nil),  // 36: dapplink.btc.VerifyTransactionRequest
	(*VerifyTransactionResponse)(nil), // 37: dapplink.btc.VerifyTransactionResponse
	(*DecodeTransactionRequest)(nil),  // 38: dapplink.btc.DecodeTransactionRequest
	(*DecodeTransactionResponse)(nil), // 39: dapplink.btc.DecodeTransactionResponse
	(common.ReturnCode)(0),            // 40: dapplink.ReturnCode
}
var file_dapplink_btc_proto_depIdxs = []int32{
	3,  // 0: dapplink.btc.TxMessage.froms:type_name -> dapplink.btc.Address
	3,  // 1: dapplink.btc.TxMessage.tos:type_name -> dapplink.btc.Address
	4,  // 2: dapplink.btc.TxMessage.values:type_name -> dapplink.btc.Value
	0,  // 3: dapplink.btc.TxMessage.status:type_name -> dapplink.btc.TxStatus
	40, // 4: dapplink.btc.SupportChainsResponse.code:type_name -> dapplink.ReturnCode
	40, // 5: dapplink.btc.ConvertAddressResponse.code:type_name -> dapplink.ReturnCode
	40, // 6: dapplink.btc.ValidAddressResponse.code:type_name -> dapplink.ReturnCode
	40, // 7: dapplink.btc.FeeResponse.code:type_name -> dapplink.ReturnCode
	40, // 8: dapplink.btc.AccountResponse.code:type_name -> dapplink.ReturnCode
	40, // 9: dapplink.btc.UnspentOutputsResponse.code:type_name -> dapplink.ReturnCode
	16, // 10: dapplink.btc.UnspentOutputsResponse.unspent_outputs:type_name -> dapplink.btc.UnspentOutput
	1,  // 11: dapplink.btc.TransactionList.vin:type_name -> dapplink.btc.Vin
	2,  // 12: dapplink.btc.TransactionList.vout:type_name -> dapplink.btc.Vout
	40, // 13: dapplink.btc.BlockResponse.code:type_name -> dapplink.ReturnCode
	21, // 14: dapplink.btc.BlockResponse.tx_list:type_name -> dapplink.btc.TransactionList
	40, // 15: dapplink.btc.BlockHeaderResponse.code:type_name -> dapplink.ReturnCode
	40, // 16: dapplink.btc.SendTxResponse.code:type_name -> dapplink.ReturnCode
	40, // 17: dapplink.btc.TxAddressResponse.code:type_name -> dapplink.ReturnCode
	5,  // 18: dapplink.btc.TxAddressResponse.tx:type_name -> dapplink.btc.TxMessage
	40, // 19: dapplink.btc.TxHashResponse.code:type_name -> dapplink.ReturnCode
	5,  // 20: dapplink.btc.TxHashResponse.tx:type_name -> dapplink.btc.TxMessage
	1,  // 21: dapplink.btc.UnSignTransactionRequest.vin:type_name -> dapplink.btc.Vin
	2,  // 22: dapplink.btc.UnSignTransactionRequest.vout:type_name -> dapplink.btc.Vout
	40, // 23: dapplink.btc.UnSignTransactionResponse.code:type_name -> dapplink.ReturnCode
	40, // 24: dapplink.btc.SignedTransactionResponse.code:type_name -> dapplink.ReturnCode
	40, // 25: dapplink.btc.VerifyTransactionResponse.code:type_name -> dapplink.ReturnCode
	1,  // 26: dapplink.btc.DecodeTransactionRequest.vins:type_name -> dapplink.btc.Vin
	40, // 27: dapplink.btc.DecodeTransactionResponse.code:type_name -> dapplink.ReturnCode
	0,  // 28: dapplink.btc.DecodeTransactionResponse.status:type_name -> dapplink.btc.TxStatus
	1,  // 29: dapplink.btc.DecodeTransactionResponse.vins:type_name -> dapplink.btc.Vin
	2,  // 30: dapplink.btc.DecodeTransactionResponse.vouts:type_name -> dapplink.btc.Vout
	6,  // 31: dapplink.btc.WalletBtcService.getSupportChains:input_type -> dapplink.btc.SupportChainsRequest
	8,  // 32: dapplink.btc.WalletBtcService.convertAddress:input_type -> dapplink.btc.ConvertAddressRequest
	10, // 33: dapplink.btc.WalletBtcService.validAddress:input_type -> dapplink.btc.ValidAddressRequest
	12, // 34: dapplink.btc.WalletBtcService.getFee:input_type -> dapplink.btc.FeeRequest
	14, // 35: dapplink.btc.WalletBtcService.getAccount:input_type -> dapplink.btc.AccountRequest
	17, // 36: dapplink.btc.WalletBtcService.getUnspentOutputs:input_type -> dapplink.btc.UnspentOutputsRequest
	19, // 37: dapplink.btc.WalletBtcService.getBlockByNumber:input_type -> dapplink.btc.BlockNumberRequest
	20, // 38: dapplink.btc.WalletBtcService.getBlockByHash:input_type -> dapplink.btc.BlockHashRequest
	23, // 39: dapplink.btc.WalletBtcService.getBlockHeaderByHash:input_type -> dapplink.btc.BlockHeaderHashRequest
	24, // 40: dapplink.btc.WalletBtcService.getBlockHeaderByNumber:input_type -> dapplink.btc.BlockHeaderNumberRequest
	26, // 41: dapplink.btc.WalletBtcService.SendTx:input_type -> dapplink.btc.SendTxRequest
	28, // 42: dapplink.btc.WalletBtcService.getTxByAddress:input_type -> dapplink.btc.TxAddressRequest
	30, // 43: dapplink.btc.WalletBtcService.getTxByHash:input_type -> dapplink.btc.TxHashRequest
	32, // 44: dapplink.btc.WalletBtcService.createUnSignTransaction:input_type -> dapplink.btc.UnSignTransactionRequest
	34, // 45: dapplink.btc.WalletBtcService.buildSignedTransaction:input_type -> dapplink.btc.SignedTransactionRequest
	38, // 46: dapplink.btc.WalletBtcService.decodeTransaction:input_type -> dapplink.btc.DecodeTransactionRequest
	36, // 47: dapplink.btc.WalletBtcService.verifySignedTransaction:input_type -> dapplink.btc.VerifyTransactionRequest
	7,  // 48: dapplink.btc.WalletBtcService.getSupportChains:output_type -> dapplink.btc.SupportChainsResponse
	9,  // 49: dapplink.btc.WalletBtcService.convertAddress:output_type -> dapplink.btc.ConvertAddressResponse
	11, // 50: dapplink.btc.WalletBtcService.validAddress:output_type -> dapplink.btc.ValidAddressResponse
	13, // 51: dapplink.btc.WalletBtcService.getFee:output_type -> dapplink.btc.FeeResponse
	15, // 52: dapplink.btc.WalletBtcService.getAccount:output_type -> dapplink.btc.AccountResponse
	18, // 53: dapplink.btc.WalletBtcService.getUnspentOutputs:output_type -> dapplink.btc.UnspentOutputsResponse
	22, // 54: dapplink.btc.WalletBtcService.getBlockByNumber:output_type -> dapplink.btc.BlockResponse
	22, // 55: dapplink.btc.WalletBtcService.getBlockByHash:output_type -> dapplink.btc.BlockResponse
	25, // 56: dapplink.btc.WalletBtcService.getBlockHeaderByHash:output_type -> dapplink.btc.BlockHeaderResponse
	25, // 57: dapplink.btc.WalletBtcService.getBlockHeaderByNumber:output_type -> dapplink.btc.BlockHeaderResponse
	27, // 58: dapplink.btc.WalletBtcService.SendTx:output_type -> dapplink.btc.SendTxResponse
	29, // 59: dapplink.btc.WalletBtcService.getTxByAddress:output_type -> dapplink.btc.TxAddressResponse
	31, // 60: dapplink.btc.WalletBtcService.getTxByHash:output_type -> dapplink.btc.TxHashResponse
	33, // 61: dapplink.btc.WalletBtcService.createUnSignTransaction:output_type -> dapplink.btc.UnSignTransactionResponse
	35, // 62: dapplink.btc.WalletBtcService.buildSignedTransaction:output_type -> dapplink.btc.SignedTransactionResponse
	39, // 63: dapplink.btc.WalletBtcService.decodeTransaction:output_type -> dapplink.btc.DecodeTransactionResponse
	37, // 64: dapplink.btc.WalletBtcService.verifySignedTransaction:output_type -> dapplink.btc.VerifyTransactionResponse
	48, // [48:65] is the sub-list for method output_type
	31, // [31:48] is the sub-list for method input_type
	31, // [31:31] is the sub-list for extension type_name
	31, // [31:31] is the sub-list for extension extendee
	0,  // [0:31] is the sub-list for field type_name
}

func init() { file_dapplink_btc_proto_init() }
func file_dapplink_btc_proto_init() {
	if File_dapplink_btc_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_dapplink_btc_proto_rawDesc), len(file_dapplink_btc_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   39,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_dapplink_btc_proto_goTypes,
		DependencyIndexes: file_dapplink_btc_proto_depIdxs,
		EnumInfos:         file_dapplink_btc_proto_enumTypes,
		MessageInfos:      file_dapplink_btc_proto_msgTypes,
	}.Build()
	File_dapplink_btc_proto = out.File
	file_dapplink_btc_proto_goTypes = nil
	file_dapplink_btc_proto_depIdxs = nil
}
