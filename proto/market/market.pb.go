// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v4.25.3
// source: dapplink/market.proto

package market

import (
	common "./proto/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Exchange struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type          string                 `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Exchange) Reset() {
	*x = Exchange{}
	mi := &file_dapplink_market_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Exchange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Exchange) ProtoMessage() {}

func (x *Exchange) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_market_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Exchange.ProtoReflect.Descriptor instead.
func (*Exchange) Descriptor() ([]byte, []int) {
	return file_dapplink_market_proto_rawDescGZIP(), []int{0}
}

func (x *Exchange) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Exchange) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Exchange) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type Asset struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Asset) Reset() {
	*x = Asset{}
	mi := &file_dapplink_market_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Asset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Asset) ProtoMessage() {}

func (x *Asset) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_market_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Asset.ProtoReflect.Descriptor instead.
func (*Asset) Descriptor() ([]byte, []int) {
	return file_dapplink_market_proto_rawDescGZIP(), []int{1}
}

func (x *Asset) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Asset) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type Symbol struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Base          string                 `protobuf:"bytes,3,opt,name=base,proto3" json:"base,omitempty"`
	Quote         string                 `protobuf:"bytes,4,opt,name=quote,proto3" json:"quote,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Symbol) Reset() {
	*x = Symbol{}
	mi := &file_dapplink_market_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Symbol) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Symbol) ProtoMessage() {}

func (x *Symbol) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_market_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Symbol.ProtoReflect.Descriptor instead.
func (*Symbol) Descriptor() ([]byte, []int) {
	return file_dapplink_market_proto_rawDescGZIP(), []int{2}
}

func (x *Symbol) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Symbol) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Symbol) GetBase() string {
	if x != nil {
		return x.Base
	}
	return ""
}

func (x *Symbol) GetQuote() string {
	if x != nil {
		return x.Quote
	}
	return ""
}

type SymbolPrice struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Base          string                 `protobuf:"bytes,3,opt,name=base,proto3" json:"base,omitempty"`
	Quote         string                 `protobuf:"bytes,4,opt,name=quote,proto3" json:"quote,omitempty"`
	Exchange      string                 `protobuf:"bytes,5,opt,name=exchange,proto3" json:"exchange,omitempty"`
	Symbol        string                 `protobuf:"bytes,6,opt,name=symbol,proto3" json:"symbol,omitempty"`
	BuyPrice      string                 `protobuf:"bytes,7,opt,name=buy_price,json=buyPrice,proto3" json:"buy_price,omitempty"`
	SellPrice     string                 `protobuf:"bytes,8,opt,name=sell_price,json=sellPrice,proto3" json:"sell_price,omitempty"`
	AvgPrice      string                 `protobuf:"bytes,9,opt,name=avg_price,json=avgPrice,proto3" json:"avg_price,omitempty"`
	UsdPrice      string                 `protobuf:"bytes,10,opt,name=usd_price,json=usdPrice,proto3" json:"usd_price,omitempty"`
	CnyPrice      string                 `protobuf:"bytes,11,opt,name=cny_price,json=cnyPrice,proto3" json:"cny_price,omitempty"`
	Margin        string                 `protobuf:"bytes,12,opt,name=margin,proto3" json:"margin,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SymbolPrice) Reset() {
	*x = SymbolPrice{}
	mi := &file_dapplink_market_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SymbolPrice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SymbolPrice) ProtoMessage() {}

func (x *SymbolPrice) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_market_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SymbolPrice.ProtoReflect.Descriptor instead.
func (*SymbolPrice) Descriptor() ([]byte, []int) {
	return file_dapplink_market_proto_rawDescGZIP(), []int{3}
}

func (x *SymbolPrice) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SymbolPrice) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SymbolPrice) GetBase() string {
	if x != nil {
		return x.Base
	}
	return ""
}

func (x *SymbolPrice) GetQuote() string {
	if x != nil {
		return x.Quote
	}
	return ""
}

func (x *SymbolPrice) GetExchange() string {
	if x != nil {
		return x.Exchange
	}
	return ""
}

func (x *SymbolPrice) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *SymbolPrice) GetBuyPrice() string {
	if x != nil {
		return x.BuyPrice
	}
	return ""
}

func (x *SymbolPrice) GetSellPrice() string {
	if x != nil {
		return x.SellPrice
	}
	return ""
}

func (x *SymbolPrice) GetAvgPrice() string {
	if x != nil {
		return x.AvgPrice
	}
	return ""
}

func (x *SymbolPrice) GetUsdPrice() string {
	if x != nil {
		return x.UsdPrice
	}
	return ""
}

func (x *SymbolPrice) GetCnyPrice() string {
	if x != nil {
		return x.CnyPrice
	}
	return ""
}

func (x *SymbolPrice) GetMargin() string {
	if x != nil {
		return x.Margin
	}
	return ""
}

type StableCoin struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StableCoin) Reset() {
	*x = StableCoin{}
	mi := &file_dapplink_market_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StableCoin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StableCoin) ProtoMessage() {}

func (x *StableCoin) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_market_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StableCoin.ProtoReflect.Descriptor instead.
func (*StableCoin) Descriptor() ([]byte, []int) {
	return file_dapplink_market_proto_rawDescGZIP(), []int{4}
}

func (x *StableCoin) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *StableCoin) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type StableCoinPrice struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	UsdPrice      string                 `protobuf:"bytes,3,opt,name=usd_price,json=usdPrice,proto3" json:"usd_price,omitempty"`
	CnyPrice      string                 `protobuf:"bytes,4,opt,name=cny_price,json=cnyPrice,proto3" json:"cny_price,omitempty"`
	Margin        string                 `protobuf:"bytes,5,opt,name=margin,proto3" json:"margin,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StableCoinPrice) Reset() {
	*x = StableCoinPrice{}
	mi := &file_dapplink_market_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StableCoinPrice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StableCoinPrice) ProtoMessage() {}

func (x *StableCoinPrice) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_market_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StableCoinPrice.ProtoReflect.Descriptor instead.
func (*StableCoinPrice) Descriptor() ([]byte, []int) {
	return file_dapplink_market_proto_rawDescGZIP(), []int{5}
}

func (x *StableCoinPrice) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *StableCoinPrice) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *StableCoinPrice) GetUsdPrice() string {
	if x != nil {
		return x.UsdPrice
	}
	return ""
}

func (x *StableCoinPrice) GetCnyPrice() string {
	if x != nil {
		return x.CnyPrice
	}
	return ""
}

func (x *StableCoinPrice) GetMargin() string {
	if x != nil {
		return x.Margin
	}
	return ""
}

type ExchangeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExchangeRequest) Reset() {
	*x = ExchangeRequest{}
	mi := &file_dapplink_market_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExchangeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeRequest) ProtoMessage() {}

func (x *ExchangeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_market_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeRequest.ProtoReflect.Descriptor instead.
func (*ExchangeRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_market_proto_rawDescGZIP(), []int{6}
}

func (x *ExchangeRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

type ExchangeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Exchanges     []*Exchange            `protobuf:"bytes,3,rep,name=exchanges,proto3" json:"exchanges,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExchangeResponse) Reset() {
	*x = ExchangeResponse{}
	mi := &file_dapplink_market_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExchangeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeResponse) ProtoMessage() {}

func (x *ExchangeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_market_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeResponse.ProtoReflect.Descriptor instead.
func (*ExchangeResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_market_proto_rawDescGZIP(), []int{7}
}

func (x *ExchangeResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *ExchangeResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ExchangeResponse) GetExchanges() []*Exchange {
	if x != nil {
		return x.Exchanges
	}
	return nil
}

type AssetRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	ExchangeId    string                 `protobuf:"bytes,2,opt,name=exchange_id,json=exchangeId,proto3" json:"exchange_id,omitempty"`
	IsBase        bool                   `protobuf:"varint,3,opt,name=is_base,json=isBase,proto3" json:"is_base,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssetRequest) Reset() {
	*x = AssetRequest{}
	mi := &file_dapplink_market_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetRequest) ProtoMessage() {}

func (x *AssetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_market_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetRequest.ProtoReflect.Descriptor instead.
func (*AssetRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_market_proto_rawDescGZIP(), []int{8}
}

func (x *AssetRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *AssetRequest) GetExchangeId() string {
	if x != nil {
		return x.ExchangeId
	}
	return ""
}

func (x *AssetRequest) GetIsBase() bool {
	if x != nil {
		return x.IsBase
	}
	return false
}

type AssetResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Assets        []*Asset               `protobuf:"bytes,3,rep,name=assets,proto3" json:"assets,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssetResponse) Reset() {
	*x = AssetResponse{}
	mi := &file_dapplink_market_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetResponse) ProtoMessage() {}

func (x *AssetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_market_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetResponse.ProtoReflect.Descriptor instead.
func (*AssetResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_market_proto_rawDescGZIP(), []int{9}
}

func (x *AssetResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *AssetResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AssetResponse) GetAssets() []*Asset {
	if x != nil {
		return x.Assets
	}
	return nil
}

type SymbolRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	ExchangeId    string                 `protobuf:"bytes,2,opt,name=exchange_id,json=exchangeId,proto3" json:"exchange_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SymbolRequest) Reset() {
	*x = SymbolRequest{}
	mi := &file_dapplink_market_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SymbolRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SymbolRequest) ProtoMessage() {}

func (x *SymbolRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_market_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SymbolRequest.ProtoReflect.Descriptor instead.
func (*SymbolRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_market_proto_rawDescGZIP(), []int{10}
}

func (x *SymbolRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *SymbolRequest) GetExchangeId() string {
	if x != nil {
		return x.ExchangeId
	}
	return ""
}

type SymbolResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Symbols       []*Symbol              `protobuf:"bytes,3,rep,name=symbols,proto3" json:"symbols,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SymbolResponse) Reset() {
	*x = SymbolResponse{}
	mi := &file_dapplink_market_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SymbolResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SymbolResponse) ProtoMessage() {}

func (x *SymbolResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_market_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SymbolResponse.ProtoReflect.Descriptor instead.
func (*SymbolResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_market_proto_rawDescGZIP(), []int{11}
}

func (x *SymbolResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *SymbolResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SymbolResponse) GetSymbols() []*Symbol {
	if x != nil {
		return x.Symbols
	}
	return nil
}

type SymbolPriceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	ExchangeId    string                 `protobuf:"bytes,2,opt,name=exchange_id,json=exchangeId,proto3" json:"exchange_id,omitempty"`
	SymbolId      string                 `protobuf:"bytes,3,opt,name=symbol_id,json=symbolId,proto3" json:"symbol_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SymbolPriceRequest) Reset() {
	*x = SymbolPriceRequest{}
	mi := &file_dapplink_market_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SymbolPriceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SymbolPriceRequest) ProtoMessage() {}

func (x *SymbolPriceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_market_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SymbolPriceRequest.ProtoReflect.Descriptor instead.
func (*SymbolPriceRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_market_proto_rawDescGZIP(), []int{12}
}

func (x *SymbolPriceRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *SymbolPriceRequest) GetExchangeId() string {
	if x != nil {
		return x.ExchangeId
	}
	return ""
}

func (x *SymbolPriceRequest) GetSymbolId() string {
	if x != nil {
		return x.SymbolId
	}
	return ""
}

type SymbolPriceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	SymbolPrices  []*SymbolPrice         `protobuf:"bytes,3,rep,name=symbol_prices,json=symbolPrices,proto3" json:"symbol_prices,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SymbolPriceResponse) Reset() {
	*x = SymbolPriceResponse{}
	mi := &file_dapplink_market_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SymbolPriceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SymbolPriceResponse) ProtoMessage() {}

func (x *SymbolPriceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_market_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SymbolPriceResponse.ProtoReflect.Descriptor instead.
func (*SymbolPriceResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_market_proto_rawDescGZIP(), []int{13}
}

func (x *SymbolPriceResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *SymbolPriceResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SymbolPriceResponse) GetSymbolPrices() []*SymbolPrice {
	if x != nil {
		return x.SymbolPrices
	}
	return nil
}

type StableCoinRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StableCoinRequest) Reset() {
	*x = StableCoinRequest{}
	mi := &file_dapplink_market_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StableCoinRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StableCoinRequest) ProtoMessage() {}

func (x *StableCoinRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_market_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StableCoinRequest.ProtoReflect.Descriptor instead.
func (*StableCoinRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_market_proto_rawDescGZIP(), []int{14}
}

func (x *StableCoinRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

type StableCoinResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	StableCoins   []*StableCoin          `protobuf:"bytes,3,rep,name=stable_coins,json=stableCoins,proto3" json:"stable_coins,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StableCoinResponse) Reset() {
	*x = StableCoinResponse{}
	mi := &file_dapplink_market_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StableCoinResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StableCoinResponse) ProtoMessage() {}

func (x *StableCoinResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_market_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StableCoinResponse.ProtoReflect.Descriptor instead.
func (*StableCoinResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_market_proto_rawDescGZIP(), []int{15}
}

func (x *StableCoinResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *StableCoinResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *StableCoinResponse) GetStableCoins() []*StableCoin {
	if x != nil {
		return x.StableCoins
	}
	return nil
}

type StableCoinPriceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	CoinId        string                 `protobuf:"bytes,2,opt,name=coin_id,json=coinId,proto3" json:"coin_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StableCoinPriceRequest) Reset() {
	*x = StableCoinPriceRequest{}
	mi := &file_dapplink_market_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StableCoinPriceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StableCoinPriceRequest) ProtoMessage() {}

func (x *StableCoinPriceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_market_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StableCoinPriceRequest.ProtoReflect.Descriptor instead.
func (*StableCoinPriceRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_market_proto_rawDescGZIP(), []int{16}
}

func (x *StableCoinPriceRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *StableCoinPriceRequest) GetCoinId() string {
	if x != nil {
		return x.CoinId
	}
	return ""
}

type StableCoinPriceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	CoinPrices    []*StableCoinPrice     `protobuf:"bytes,3,rep,name=coin_prices,json=coinPrices,proto3" json:"coin_prices,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StableCoinPriceResponse) Reset() {
	*x = StableCoinPriceResponse{}
	mi := &file_dapplink_market_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StableCoinPriceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StableCoinPriceResponse) ProtoMessage() {}

func (x *StableCoinPriceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_market_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StableCoinPriceResponse.ProtoReflect.Descriptor instead.
func (*StableCoinPriceResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_market_proto_rawDescGZIP(), []int{17}
}

func (x *StableCoinPriceResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *StableCoinPriceResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *StableCoinPriceResponse) GetCoinPrices() []*StableCoinPrice {
	if x != nil {
		return x.CoinPrices
	}
	return nil
}

var File_dapplink_market_proto protoreflect.FileDescriptor

var file_dapplink_market_proto_rawDesc = string([]byte{
	0x0a, 0x15, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x1a, 0x15, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x42, 0x0a, 0x08, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x22, 0x2b, 0x0a, 0x05, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x22, 0x56, 0x0a, 0x06, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x62, 0x61, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x62, 0x61,
	0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x22, 0xba, 0x02, 0x0a, 0x0b, 0x53, 0x79, 0x6d,
	0x62, 0x6f, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x62, 0x61, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x62, 0x61, 0x73, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x75,
	0x79, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62,
	0x75, 0x79, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x6c, 0x6c, 0x5f,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x6c,
	0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x76, 0x67, 0x5f, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x76, 0x67, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x64, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x64, 0x50, 0x72, 0x69, 0x63, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6e, 0x79, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6e, 0x79, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x6d, 0x61, 0x72, 0x67, 0x69, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d,
	0x61, 0x72, 0x67, 0x69, 0x6e, 0x22, 0x30, 0x0a, 0x0a, 0x53, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x43,
	0x6f, 0x69, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x87, 0x01, 0x0a, 0x0f, 0x53, 0x74, 0x61, 0x62,
	0x6c, 0x65, 0x43, 0x6f, 0x69, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x75, 0x73, 0x64, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x64, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x63, 0x6e, 0x79, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x63, 0x6e, 0x79, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x61, 0x72,
	0x67, 0x69, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x61, 0x72, 0x67, 0x69,
	0x6e, 0x22, 0x38, 0x0a, 0x0f, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x87, 0x01, 0x0a, 0x10,
	0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14,
	0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e,
	0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x37, 0x0a, 0x09,
	0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x09, 0x65, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x73, 0x22, 0x6f, 0x0a, 0x0c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1f, 0x0a, 0x0b,
	0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x49, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x69, 0x73, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06,
	0x69, 0x73, 0x42, 0x61, 0x73, 0x65, 0x22, 0x7b, 0x0a, 0x0d, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x12, 0x2e, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x6d,
	0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x06, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x73, 0x22, 0x57, 0x0a, 0x0d, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x65,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x49, 0x64, 0x22, 0x7f, 0x0a, 0x0e,
	0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f,
	0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x31, 0x0a, 0x07, 0x73, 0x79,
	0x6d, 0x62, 0x6f, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x64, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x53, 0x79,
	0x6d, 0x62, 0x6f, 0x6c, 0x52, 0x07, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x73, 0x22, 0x79, 0x0a,
	0x12, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73,
	0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x49, 0x64, 0x22, 0x94, 0x01, 0x0a, 0x13, 0x53, 0x79, 0x6d,
	0x62, 0x6f, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14,
	0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e,
	0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x41, 0x0a, 0x0d,
	0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x6d,
	0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x52, 0x0c, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x73, 0x22,
	0x3a, 0x0a, 0x11, 0x53, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x69, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x90, 0x01, 0x0a, 0x12,
	0x53, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75,
	0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x3e,
	0x0a, 0x0c, 0x73, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x69,
	0x6e, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x22, 0x58,
	0x0a, 0x16, 0x53, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x69, 0x6e, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12,
	0x17, 0x0a, 0x07, 0x63, 0x6f, 0x69, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x63, 0x6f, 0x69, 0x6e, 0x49, 0x64, 0x22, 0x98, 0x01, 0x0a, 0x17, 0x53, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x43, 0x6f, 0x69, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65,
	0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67,
	0x12, 0x41, 0x0a, 0x0b, 0x63, 0x6f, 0x69, 0x6e, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f,
	0x69, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x0a, 0x63, 0x6f, 0x69, 0x6e, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x73, 0x32, 0xac, 0x04, 0x0a, 0x0c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x55, 0x0a, 0x0c, 0x67, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x73, 0x12, 0x20, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4c, 0x0a, 0x09, 0x67,
	0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12, 0x1d, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4f, 0x0a, 0x0a, 0x67, 0x65, 0x74,
	0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x73, 0x12, 0x1e, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5e, 0x0a, 0x0f, 0x67, 0x65,
	0x74, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x73, 0x12, 0x23, 0x2e,
	0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e,
	0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x24, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x6d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x2e, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5b, 0x0a, 0x0e, 0x67, 0x65,
	0x74, 0x53, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x12, 0x22, 0x2e, 0x64,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x53,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x23, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b,
	0x65, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x69, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x69, 0x0a, 0x12, 0x67, 0x65, 0x74, 0x53, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x69, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x27, 0x2e,
	0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e,
	0x53, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x69, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x43,
	0x6f, 0x69, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x42, 0x25, 0x0a, 0x13, 0x78, 0x79, 0x7a, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x5a, 0x0e, 0x2e, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
})

var (
	file_dapplink_market_proto_rawDescOnce sync.Once
	file_dapplink_market_proto_rawDescData []byte
)

func file_dapplink_market_proto_rawDescGZIP() []byte {
	file_dapplink_market_proto_rawDescOnce.Do(func() {
		file_dapplink_market_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_dapplink_market_proto_rawDesc), len(file_dapplink_market_proto_rawDesc)))
	})
	return file_dapplink_market_proto_rawDescData
}

var file_dapplink_market_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_dapplink_market_proto_goTypes = []any{
	(*Exchange)(nil),                // 0: dapplink.market.Exchange
	(*Asset)(nil),                   // 1: dapplink.market.Asset
	(*Symbol)(nil),                  // 2: dapplink.market.Symbol
	(*SymbolPrice)(nil),             // 3: dapplink.market.SymbolPrice
	(*StableCoin)(nil),              // 4: dapplink.market.StableCoin
	(*StableCoinPrice)(nil),         // 5: dapplink.market.StableCoinPrice
	(*ExchangeRequest)(nil),         // 6: dapplink.market.ExchangeRequest
	(*ExchangeResponse)(nil),        // 7: dapplink.market.ExchangeResponse
	(*AssetRequest)(nil),            // 8: dapplink.market.AssetRequest
	(*AssetResponse)(nil),           // 9: dapplink.market.AssetResponse
	(*SymbolRequest)(nil),           // 10: dapplink.market.SymbolRequest
	(*SymbolResponse)(nil),          // 11: dapplink.market.SymbolResponse
	(*SymbolPriceRequest)(nil),      // 12: dapplink.market.SymbolPriceRequest
	(*SymbolPriceResponse)(nil),     // 13: dapplink.market.SymbolPriceResponse
	(*StableCoinRequest)(nil),       // 14: dapplink.market.StableCoinRequest
	(*StableCoinResponse)(nil),      // 15: dapplink.market.StableCoinResponse
	(*StableCoinPriceRequest)(nil),  // 16: dapplink.market.StableCoinPriceRequest
	(*StableCoinPriceResponse)(nil), // 17: dapplink.market.StableCoinPriceResponse
	(common.ReturnCode)(0),          // 18: dapplink.ReturnCode
}
var file_dapplink_market_proto_depIdxs = []int32{
	18, // 0: dapplink.market.ExchangeResponse.code:type_name -> dapplink.ReturnCode
	0,  // 1: dapplink.market.ExchangeResponse.exchanges:type_name -> dapplink.market.Exchange
	18, // 2: dapplink.market.AssetResponse.code:type_name -> dapplink.ReturnCode
	1,  // 3: dapplink.market.AssetResponse.assets:type_name -> dapplink.market.Asset
	18, // 4: dapplink.market.SymbolResponse.code:type_name -> dapplink.ReturnCode
	2,  // 5: dapplink.market.SymbolResponse.symbols:type_name -> dapplink.market.Symbol
	18, // 6: dapplink.market.SymbolPriceResponse.code:type_name -> dapplink.ReturnCode
	3,  // 7: dapplink.market.SymbolPriceResponse.symbol_prices:type_name -> dapplink.market.SymbolPrice
	18, // 8: dapplink.market.StableCoinResponse.code:type_name -> dapplink.ReturnCode
	4,  // 9: dapplink.market.StableCoinResponse.stable_coins:type_name -> dapplink.market.StableCoin
	18, // 10: dapplink.market.StableCoinPriceResponse.code:type_name -> dapplink.ReturnCode
	5,  // 11: dapplink.market.StableCoinPriceResponse.coin_prices:type_name -> dapplink.market.StableCoinPrice
	6,  // 12: dapplink.market.PriceService.getExchanges:input_type -> dapplink.market.ExchangeRequest
	8,  // 13: dapplink.market.PriceService.getAssets:input_type -> dapplink.market.AssetRequest
	10, // 14: dapplink.market.PriceService.getSymbols:input_type -> dapplink.market.SymbolRequest
	12, // 15: dapplink.market.PriceService.getSymbolPrices:input_type -> dapplink.market.SymbolPriceRequest
	14, // 16: dapplink.market.PriceService.getStableCoins:input_type -> dapplink.market.StableCoinRequest
	16, // 17: dapplink.market.PriceService.getStableCoinPrice:input_type -> dapplink.market.StableCoinPriceRequest
	7,  // 18: dapplink.market.PriceService.getExchanges:output_type -> dapplink.market.ExchangeResponse
	9,  // 19: dapplink.market.PriceService.getAssets:output_type -> dapplink.market.AssetResponse
	11, // 20: dapplink.market.PriceService.getSymbols:output_type -> dapplink.market.SymbolResponse
	13, // 21: dapplink.market.PriceService.getSymbolPrices:output_type -> dapplink.market.SymbolPriceResponse
	15, // 22: dapplink.market.PriceService.getStableCoins:output_type -> dapplink.market.StableCoinResponse
	17, // 23: dapplink.market.PriceService.getStableCoinPrice:output_type -> dapplink.market.StableCoinPriceResponse
	18, // [18:24] is the sub-list for method output_type
	12, // [12:18] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_dapplink_market_proto_init() }
func file_dapplink_market_proto_init() {
	if File_dapplink_market_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_dapplink_market_proto_rawDesc), len(file_dapplink_market_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_dapplink_market_proto_goTypes,
		DependencyIndexes: file_dapplink_market_proto_depIdxs,
		MessageInfos:      file_dapplink_market_proto_msgTypes,
	}.Build()
	File_dapplink_market_proto = out.File
	file_dapplink_market_proto_goTypes = nil
	file_dapplink_market_proto_depIdxs = nil
}
