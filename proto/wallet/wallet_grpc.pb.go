// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v4.25.3
// source: dapplink/wallet.proto

package wallet

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	WalletService_GetSupportCoins_FullMethodName               = "/dapplink.wallet.WalletService/getSupportCoins"
	WalletService_ConvertAddress_FullMethodName                = "/dapplink.wallet.WalletService/convertAddress"
	WalletService_ValidAddress_FullMethodName                  = "/dapplink.wallet.WalletService/validAddress"
	WalletService_GetBlock_FullMethodName                      = "/dapplink.wallet.WalletService/getBlock"
	WalletService_GetBlockHeaderByNumber_FullMethodName        = "/dapplink.wallet.WalletService/getBlockHeaderByNumber"
	WalletService_GetNonce_FullMethodName                      = "/dapplink.wallet.WalletService/getNonce"
	WalletService_GetGasPrice_FullMethodName                   = "/dapplink.wallet.WalletService/getGasPrice"
	WalletService_GetBalance_FullMethodName                    = "/dapplink.wallet.WalletService/getBalance"
	WalletService_GetAccount_FullMethodName                    = "/dapplink.wallet.WalletService/getAccount"
	WalletService_GetMinRent_FullMethodName                    = "/dapplink.wallet.WalletService/getMinRent"
	WalletService_SendTx_FullMethodName                        = "/dapplink.wallet.WalletService/SendTx"
	WalletService_GetTxByAddress_FullMethodName                = "/dapplink.wallet.WalletService/getTxByAddress"
	WalletService_GetTxByHash_FullMethodName                   = "/dapplink.wallet.WalletService/getTxByHash"
	WalletService_GetUtxo_FullMethodName                       = "/dapplink.wallet.WalletService/getUtxo"
	WalletService_GetUnspentOutputs_FullMethodName             = "/dapplink.wallet.WalletService/getUnspentOutputs"
	WalletService_GetUtxoInsFromData_FullMethodName            = "/dapplink.wallet.WalletService/getUtxoInsFromData"
	WalletService_GetAccountTxFromData_FullMethodName          = "/dapplink.wallet.WalletService/getAccountTxFromData"
	WalletService_GetUtxoTxFromData_FullMethodName             = "/dapplink.wallet.WalletService/getUtxoTxFromData"
	WalletService_GetAccountTxFromSignedData_FullMethodName    = "/dapplink.wallet.WalletService/getAccountTxFromSignedData"
	WalletService_GetUtxoTxFromSignedData_FullMethodName       = "/dapplink.wallet.WalletService/GetUtxoTxFromSignedData"
	WalletService_CreateAccountSignedTx_FullMethodName         = "/dapplink.wallet.WalletService/createAccountSignedTx"
	WalletService_CreateAccountTx_FullMethodName               = "/dapplink.wallet.WalletService/createAccountTx"
	WalletService_CreateUtxoSignedTx_FullMethodName            = "/dapplink.wallet.WalletService/createUtxoSignedTx"
	WalletService_CreateUtxoTx_FullMethodName                  = "/dapplink.wallet.WalletService/createUtxoTx"
	WalletService_VerifyAccountSignedTx_FullMethodName         = "/dapplink.wallet.WalletService/verifyAccountSignedTx"
	WalletService_VerifyUtxoSignedTx_FullMethodName            = "/dapplink.wallet.WalletService/verifyUtxoSignedTx"
	WalletService_ABIBinToJSON_FullMethodName                  = "/dapplink.wallet.WalletService/ABIBinToJSON"
	WalletService_ABIJSONToBin_FullMethodName                  = "/dapplink.wallet.WalletService/ABIJSONToBin"
	WalletService_GetBlockByNumber_FullMethodName              = "/dapplink.wallet.WalletService/getBlockByNumber"
	WalletService_GetLatestSafeBlockHeader_FullMethodName      = "/dapplink.wallet.WalletService/getLatestSafeBlockHeader"
	WalletService_GetLatestFinalizedBlockHeader_FullMethodName = "/dapplink.wallet.WalletService/getLatestFinalizedBlockHeader"
	WalletService_GetBlockHeaderByHash_FullMethodName          = "/dapplink.wallet.WalletService/getBlockHeaderByHash"
	WalletService_GetBlockByRange_FullMethodName               = "/dapplink.wallet.WalletService/GetBlockByRange"
	WalletService_GetTxReceiptByHash_FullMethodName            = "/dapplink.wallet.WalletService/getTxReceiptByHash"
	WalletService_GetStorageHash_FullMethodName                = "/dapplink.wallet.WalletService/getStorageHash"
	WalletService_GetFilterLogs_FullMethodName                 = "/dapplink.wallet.WalletService/getFilterLogs"
	WalletService_GetTxCountByAddress_FullMethodName           = "/dapplink.wallet.WalletService/getTxCountByAddress"
	WalletService_GetSuggestGasPrice_FullMethodName            = "/dapplink.wallet.WalletService/getSuggestGasPrice"
	WalletService_GetSuggestGasTipCap_FullMethodName           = "/dapplink.wallet.WalletService/getSuggestGasTipCap"
)

// WalletServiceClient is the client API for WalletService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WalletServiceClient interface {
	GetSupportCoins(ctx context.Context, in *SupportCoinsRequest, opts ...grpc.CallOption) (*SupportCoinsResponse, error)
	ConvertAddress(ctx context.Context, in *ConvertAddressRequest, opts ...grpc.CallOption) (*ConvertAddressResponse, error)
	ValidAddress(ctx context.Context, in *ValidAddressRequest, opts ...grpc.CallOption) (*ValidAddressResponse, error)
	GetBlock(ctx context.Context, in *BlockRequest, opts ...grpc.CallOption) (*BlockResponse, error)
	GetBlockHeaderByNumber(ctx context.Context, in *BlockHeaderRequest, opts ...grpc.CallOption) (*BlockHeaderResponse, error)
	GetNonce(ctx context.Context, in *NonceRequest, opts ...grpc.CallOption) (*NonceResponse, error)
	GetGasPrice(ctx context.Context, in *GasPriceRequest, opts ...grpc.CallOption) (*GasPriceResponse, error)
	GetBalance(ctx context.Context, in *BalanceRequest, opts ...grpc.CallOption) (*BalanceResponse, error)
	GetAccount(ctx context.Context, in *AccountRequest, opts ...grpc.CallOption) (*AccountResponse, error)
	GetMinRent(ctx context.Context, in *MinRentRequest, opts ...grpc.CallOption) (*MinRentResponse, error)
	SendTx(ctx context.Context, in *SendTxRequest, opts ...grpc.CallOption) (*SendTxResponse, error)
	GetTxByAddress(ctx context.Context, in *TxAddressRequest, opts ...grpc.CallOption) (*TxAddressResponse, error)
	GetTxByHash(ctx context.Context, in *TxHashRequest, opts ...grpc.CallOption) (*TxHashResponse, error)
	GetUtxo(ctx context.Context, in *UtxoRequest, opts ...grpc.CallOption) (*UtxoResponse, error)
	GetUnspentOutputs(ctx context.Context, in *UnspentOutputsRequest, opts ...grpc.CallOption) (*UnspentOutputsResponse, error)
	GetUtxoInsFromData(ctx context.Context, in *UtxoInsFromDataRequest, opts ...grpc.CallOption) (*UtxoInsResponse, error)
	GetAccountTxFromData(ctx context.Context, in *TxFromDataRequest, opts ...grpc.CallOption) (*AccountTxResponse, error)
	GetUtxoTxFromData(ctx context.Context, in *TxFromDataRequest, opts ...grpc.CallOption) (*UtxoTxResponse, error)
	GetAccountTxFromSignedData(ctx context.Context, in *TxFromSignedDataRequest, opts ...grpc.CallOption) (*AccountTxResponse, error)
	GetUtxoTxFromSignedData(ctx context.Context, in *TxFromSignedDataRequest, opts ...grpc.CallOption) (*UtxoTxResponse, error)
	CreateAccountSignedTx(ctx context.Context, in *CreateAccountSignedTxRequest, opts ...grpc.CallOption) (*CreateSignedTxResponse, error)
	CreateAccountTx(ctx context.Context, in *CreateAccountTxRequest, opts ...grpc.CallOption) (*CreateAccountTxResponse, error)
	CreateUtxoSignedTx(ctx context.Context, in *CreateUtxoSignedTxRequest, opts ...grpc.CallOption) (*CreateSignedTxResponse, error)
	CreateUtxoTx(ctx context.Context, in *CreateUtxoTxRequest, opts ...grpc.CallOption) (*CreateUtxoTxResponse, error)
	VerifyAccountSignedTx(ctx context.Context, in *VerifySignedTxRequest, opts ...grpc.CallOption) (*VerifySignedTxResponse, error)
	VerifyUtxoSignedTx(ctx context.Context, in *VerifySignedTxRequest, opts ...grpc.CallOption) (*VerifySignedTxResponse, error)
	ABIBinToJSON(ctx context.Context, in *ABIBinToJSONRequest, opts ...grpc.CallOption) (*ABIBinToJSONResponse, error)
	ABIJSONToBin(ctx context.Context, in *ABIJSONToBinRequest, opts ...grpc.CallOption) (*ABIJSONToBinResponse, error)
	GetBlockByNumber(ctx context.Context, in *BlockInfoRequest, opts ...grpc.CallOption) (*BlockInfoResponse, error)
	GetLatestSafeBlockHeader(ctx context.Context, in *BasicRequest, opts ...grpc.CallOption) (*BlockHeaderResponse, error)
	GetLatestFinalizedBlockHeader(ctx context.Context, in *BasicRequest, opts ...grpc.CallOption) (*BlockHeaderResponse, error)
	GetBlockHeaderByHash(ctx context.Context, in *BlockHeaderByHashRequest, opts ...grpc.CallOption) (*BlockHeaderResponse, error)
	GetBlockByRange(ctx context.Context, in *BlockByRangeRequest, opts ...grpc.CallOption) (*BlockByRangeResponse, error)
	GetTxReceiptByHash(ctx context.Context, in *TxReceiptByHashRequest, opts ...grpc.CallOption) (*TxReceiptByHashResponse, error)
	GetStorageHash(ctx context.Context, in *StorageHashRequest, opts ...grpc.CallOption) (*StorageHashResponse, error)
	GetFilterLogs(ctx context.Context, in *FilterLogsRequest, opts ...grpc.CallOption) (*FilterLogsResponse, error)
	GetTxCountByAddress(ctx context.Context, in *TxCountByAddressRequest, opts ...grpc.CallOption) (*TxCountByAddressResponse, error)
	GetSuggestGasPrice(ctx context.Context, in *SuggestGasPriceRequest, opts ...grpc.CallOption) (*SuggestGasPriceResponse, error)
	GetSuggestGasTipCap(ctx context.Context, in *SuggestGasPriceRequest, opts ...grpc.CallOption) (*SuggestGasPriceResponse, error)
}

type walletServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWalletServiceClient(cc grpc.ClientConnInterface) WalletServiceClient {
	return &walletServiceClient{cc}
}

func (c *walletServiceClient) GetSupportCoins(ctx context.Context, in *SupportCoinsRequest, opts ...grpc.CallOption) (*SupportCoinsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SupportCoinsResponse)
	err := c.cc.Invoke(ctx, WalletService_GetSupportCoins_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) ConvertAddress(ctx context.Context, in *ConvertAddressRequest, opts ...grpc.CallOption) (*ConvertAddressResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ConvertAddressResponse)
	err := c.cc.Invoke(ctx, WalletService_ConvertAddress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) ValidAddress(ctx context.Context, in *ValidAddressRequest, opts ...grpc.CallOption) (*ValidAddressResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ValidAddressResponse)
	err := c.cc.Invoke(ctx, WalletService_ValidAddress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) GetBlock(ctx context.Context, in *BlockRequest, opts ...grpc.CallOption) (*BlockResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BlockResponse)
	err := c.cc.Invoke(ctx, WalletService_GetBlock_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) GetBlockHeaderByNumber(ctx context.Context, in *BlockHeaderRequest, opts ...grpc.CallOption) (*BlockHeaderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BlockHeaderResponse)
	err := c.cc.Invoke(ctx, WalletService_GetBlockHeaderByNumber_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) GetNonce(ctx context.Context, in *NonceRequest, opts ...grpc.CallOption) (*NonceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NonceResponse)
	err := c.cc.Invoke(ctx, WalletService_GetNonce_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) GetGasPrice(ctx context.Context, in *GasPriceRequest, opts ...grpc.CallOption) (*GasPriceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GasPriceResponse)
	err := c.cc.Invoke(ctx, WalletService_GetGasPrice_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) GetBalance(ctx context.Context, in *BalanceRequest, opts ...grpc.CallOption) (*BalanceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BalanceResponse)
	err := c.cc.Invoke(ctx, WalletService_GetBalance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) GetAccount(ctx context.Context, in *AccountRequest, opts ...grpc.CallOption) (*AccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AccountResponse)
	err := c.cc.Invoke(ctx, WalletService_GetAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) GetMinRent(ctx context.Context, in *MinRentRequest, opts ...grpc.CallOption) (*MinRentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MinRentResponse)
	err := c.cc.Invoke(ctx, WalletService_GetMinRent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) SendTx(ctx context.Context, in *SendTxRequest, opts ...grpc.CallOption) (*SendTxResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendTxResponse)
	err := c.cc.Invoke(ctx, WalletService_SendTx_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) GetTxByAddress(ctx context.Context, in *TxAddressRequest, opts ...grpc.CallOption) (*TxAddressResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TxAddressResponse)
	err := c.cc.Invoke(ctx, WalletService_GetTxByAddress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) GetTxByHash(ctx context.Context, in *TxHashRequest, opts ...grpc.CallOption) (*TxHashResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TxHashResponse)
	err := c.cc.Invoke(ctx, WalletService_GetTxByHash_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) GetUtxo(ctx context.Context, in *UtxoRequest, opts ...grpc.CallOption) (*UtxoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UtxoResponse)
	err := c.cc.Invoke(ctx, WalletService_GetUtxo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) GetUnspentOutputs(ctx context.Context, in *UnspentOutputsRequest, opts ...grpc.CallOption) (*UnspentOutputsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UnspentOutputsResponse)
	err := c.cc.Invoke(ctx, WalletService_GetUnspentOutputs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) GetUtxoInsFromData(ctx context.Context, in *UtxoInsFromDataRequest, opts ...grpc.CallOption) (*UtxoInsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UtxoInsResponse)
	err := c.cc.Invoke(ctx, WalletService_GetUtxoInsFromData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) GetAccountTxFromData(ctx context.Context, in *TxFromDataRequest, opts ...grpc.CallOption) (*AccountTxResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AccountTxResponse)
	err := c.cc.Invoke(ctx, WalletService_GetAccountTxFromData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) GetUtxoTxFromData(ctx context.Context, in *TxFromDataRequest, opts ...grpc.CallOption) (*UtxoTxResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UtxoTxResponse)
	err := c.cc.Invoke(ctx, WalletService_GetUtxoTxFromData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) GetAccountTxFromSignedData(ctx context.Context, in *TxFromSignedDataRequest, opts ...grpc.CallOption) (*AccountTxResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AccountTxResponse)
	err := c.cc.Invoke(ctx, WalletService_GetAccountTxFromSignedData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) GetUtxoTxFromSignedData(ctx context.Context, in *TxFromSignedDataRequest, opts ...grpc.CallOption) (*UtxoTxResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UtxoTxResponse)
	err := c.cc.Invoke(ctx, WalletService_GetUtxoTxFromSignedData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) CreateAccountSignedTx(ctx context.Context, in *CreateAccountSignedTxRequest, opts ...grpc.CallOption) (*CreateSignedTxResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateSignedTxResponse)
	err := c.cc.Invoke(ctx, WalletService_CreateAccountSignedTx_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) CreateAccountTx(ctx context.Context, in *CreateAccountTxRequest, opts ...grpc.CallOption) (*CreateAccountTxResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateAccountTxResponse)
	err := c.cc.Invoke(ctx, WalletService_CreateAccountTx_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) CreateUtxoSignedTx(ctx context.Context, in *CreateUtxoSignedTxRequest, opts ...grpc.CallOption) (*CreateSignedTxResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateSignedTxResponse)
	err := c.cc.Invoke(ctx, WalletService_CreateUtxoSignedTx_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) CreateUtxoTx(ctx context.Context, in *CreateUtxoTxRequest, opts ...grpc.CallOption) (*CreateUtxoTxResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateUtxoTxResponse)
	err := c.cc.Invoke(ctx, WalletService_CreateUtxoTx_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) VerifyAccountSignedTx(ctx context.Context, in *VerifySignedTxRequest, opts ...grpc.CallOption) (*VerifySignedTxResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VerifySignedTxResponse)
	err := c.cc.Invoke(ctx, WalletService_VerifyAccountSignedTx_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) VerifyUtxoSignedTx(ctx context.Context, in *VerifySignedTxRequest, opts ...grpc.CallOption) (*VerifySignedTxResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VerifySignedTxResponse)
	err := c.cc.Invoke(ctx, WalletService_VerifyUtxoSignedTx_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) ABIBinToJSON(ctx context.Context, in *ABIBinToJSONRequest, opts ...grpc.CallOption) (*ABIBinToJSONResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ABIBinToJSONResponse)
	err := c.cc.Invoke(ctx, WalletService_ABIBinToJSON_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) ABIJSONToBin(ctx context.Context, in *ABIJSONToBinRequest, opts ...grpc.CallOption) (*ABIJSONToBinResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ABIJSONToBinResponse)
	err := c.cc.Invoke(ctx, WalletService_ABIJSONToBin_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) GetBlockByNumber(ctx context.Context, in *BlockInfoRequest, opts ...grpc.CallOption) (*BlockInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BlockInfoResponse)
	err := c.cc.Invoke(ctx, WalletService_GetBlockByNumber_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) GetLatestSafeBlockHeader(ctx context.Context, in *BasicRequest, opts ...grpc.CallOption) (*BlockHeaderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BlockHeaderResponse)
	err := c.cc.Invoke(ctx, WalletService_GetLatestSafeBlockHeader_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) GetLatestFinalizedBlockHeader(ctx context.Context, in *BasicRequest, opts ...grpc.CallOption) (*BlockHeaderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BlockHeaderResponse)
	err := c.cc.Invoke(ctx, WalletService_GetLatestFinalizedBlockHeader_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) GetBlockHeaderByHash(ctx context.Context, in *BlockHeaderByHashRequest, opts ...grpc.CallOption) (*BlockHeaderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BlockHeaderResponse)
	err := c.cc.Invoke(ctx, WalletService_GetBlockHeaderByHash_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) GetBlockByRange(ctx context.Context, in *BlockByRangeRequest, opts ...grpc.CallOption) (*BlockByRangeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BlockByRangeResponse)
	err := c.cc.Invoke(ctx, WalletService_GetBlockByRange_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) GetTxReceiptByHash(ctx context.Context, in *TxReceiptByHashRequest, opts ...grpc.CallOption) (*TxReceiptByHashResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TxReceiptByHashResponse)
	err := c.cc.Invoke(ctx, WalletService_GetTxReceiptByHash_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) GetStorageHash(ctx context.Context, in *StorageHashRequest, opts ...grpc.CallOption) (*StorageHashResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StorageHashResponse)
	err := c.cc.Invoke(ctx, WalletService_GetStorageHash_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) GetFilterLogs(ctx context.Context, in *FilterLogsRequest, opts ...grpc.CallOption) (*FilterLogsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FilterLogsResponse)
	err := c.cc.Invoke(ctx, WalletService_GetFilterLogs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) GetTxCountByAddress(ctx context.Context, in *TxCountByAddressRequest, opts ...grpc.CallOption) (*TxCountByAddressResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TxCountByAddressResponse)
	err := c.cc.Invoke(ctx, WalletService_GetTxCountByAddress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) GetSuggestGasPrice(ctx context.Context, in *SuggestGasPriceRequest, opts ...grpc.CallOption) (*SuggestGasPriceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SuggestGasPriceResponse)
	err := c.cc.Invoke(ctx, WalletService_GetSuggestGasPrice_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletServiceClient) GetSuggestGasTipCap(ctx context.Context, in *SuggestGasPriceRequest, opts ...grpc.CallOption) (*SuggestGasPriceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SuggestGasPriceResponse)
	err := c.cc.Invoke(ctx, WalletService_GetSuggestGasTipCap_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WalletServiceServer is the server API for WalletService service.
// All implementations should embed UnimplementedWalletServiceServer
// for forward compatibility.
type WalletServiceServer interface {
	GetSupportCoins(context.Context, *SupportCoinsRequest) (*SupportCoinsResponse, error)
	ConvertAddress(context.Context, *ConvertAddressRequest) (*ConvertAddressResponse, error)
	ValidAddress(context.Context, *ValidAddressRequest) (*ValidAddressResponse, error)
	GetBlock(context.Context, *BlockRequest) (*BlockResponse, error)
	GetBlockHeaderByNumber(context.Context, *BlockHeaderRequest) (*BlockHeaderResponse, error)
	GetNonce(context.Context, *NonceRequest) (*NonceResponse, error)
	GetGasPrice(context.Context, *GasPriceRequest) (*GasPriceResponse, error)
	GetBalance(context.Context, *BalanceRequest) (*BalanceResponse, error)
	GetAccount(context.Context, *AccountRequest) (*AccountResponse, error)
	GetMinRent(context.Context, *MinRentRequest) (*MinRentResponse, error)
	SendTx(context.Context, *SendTxRequest) (*SendTxResponse, error)
	GetTxByAddress(context.Context, *TxAddressRequest) (*TxAddressResponse, error)
	GetTxByHash(context.Context, *TxHashRequest) (*TxHashResponse, error)
	GetUtxo(context.Context, *UtxoRequest) (*UtxoResponse, error)
	GetUnspentOutputs(context.Context, *UnspentOutputsRequest) (*UnspentOutputsResponse, error)
	GetUtxoInsFromData(context.Context, *UtxoInsFromDataRequest) (*UtxoInsResponse, error)
	GetAccountTxFromData(context.Context, *TxFromDataRequest) (*AccountTxResponse, error)
	GetUtxoTxFromData(context.Context, *TxFromDataRequest) (*UtxoTxResponse, error)
	GetAccountTxFromSignedData(context.Context, *TxFromSignedDataRequest) (*AccountTxResponse, error)
	GetUtxoTxFromSignedData(context.Context, *TxFromSignedDataRequest) (*UtxoTxResponse, error)
	CreateAccountSignedTx(context.Context, *CreateAccountSignedTxRequest) (*CreateSignedTxResponse, error)
	CreateAccountTx(context.Context, *CreateAccountTxRequest) (*CreateAccountTxResponse, error)
	CreateUtxoSignedTx(context.Context, *CreateUtxoSignedTxRequest) (*CreateSignedTxResponse, error)
	CreateUtxoTx(context.Context, *CreateUtxoTxRequest) (*CreateUtxoTxResponse, error)
	VerifyAccountSignedTx(context.Context, *VerifySignedTxRequest) (*VerifySignedTxResponse, error)
	VerifyUtxoSignedTx(context.Context, *VerifySignedTxRequest) (*VerifySignedTxResponse, error)
	ABIBinToJSON(context.Context, *ABIBinToJSONRequest) (*ABIBinToJSONResponse, error)
	ABIJSONToBin(context.Context, *ABIJSONToBinRequest) (*ABIJSONToBinResponse, error)
	GetBlockByNumber(context.Context, *BlockInfoRequest) (*BlockInfoResponse, error)
	GetLatestSafeBlockHeader(context.Context, *BasicRequest) (*BlockHeaderResponse, error)
	GetLatestFinalizedBlockHeader(context.Context, *BasicRequest) (*BlockHeaderResponse, error)
	GetBlockHeaderByHash(context.Context, *BlockHeaderByHashRequest) (*BlockHeaderResponse, error)
	GetBlockByRange(context.Context, *BlockByRangeRequest) (*BlockByRangeResponse, error)
	GetTxReceiptByHash(context.Context, *TxReceiptByHashRequest) (*TxReceiptByHashResponse, error)
	GetStorageHash(context.Context, *StorageHashRequest) (*StorageHashResponse, error)
	GetFilterLogs(context.Context, *FilterLogsRequest) (*FilterLogsResponse, error)
	GetTxCountByAddress(context.Context, *TxCountByAddressRequest) (*TxCountByAddressResponse, error)
	GetSuggestGasPrice(context.Context, *SuggestGasPriceRequest) (*SuggestGasPriceResponse, error)
	GetSuggestGasTipCap(context.Context, *SuggestGasPriceRequest) (*SuggestGasPriceResponse, error)
}

// UnimplementedWalletServiceServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedWalletServiceServer struct{}

func (UnimplementedWalletServiceServer) GetSupportCoins(context.Context, *SupportCoinsRequest) (*SupportCoinsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSupportCoins not implemented")
}
func (UnimplementedWalletServiceServer) ConvertAddress(context.Context, *ConvertAddressRequest) (*ConvertAddressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConvertAddress not implemented")
}
func (UnimplementedWalletServiceServer) ValidAddress(context.Context, *ValidAddressRequest) (*ValidAddressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidAddress not implemented")
}
func (UnimplementedWalletServiceServer) GetBlock(context.Context, *BlockRequest) (*BlockResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlock not implemented")
}
func (UnimplementedWalletServiceServer) GetBlockHeaderByNumber(context.Context, *BlockHeaderRequest) (*BlockHeaderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlockHeaderByNumber not implemented")
}
func (UnimplementedWalletServiceServer) GetNonce(context.Context, *NonceRequest) (*NonceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNonce not implemented")
}
func (UnimplementedWalletServiceServer) GetGasPrice(context.Context, *GasPriceRequest) (*GasPriceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGasPrice not implemented")
}
func (UnimplementedWalletServiceServer) GetBalance(context.Context, *BalanceRequest) (*BalanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBalance not implemented")
}
func (UnimplementedWalletServiceServer) GetAccount(context.Context, *AccountRequest) (*AccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccount not implemented")
}
func (UnimplementedWalletServiceServer) GetMinRent(context.Context, *MinRentRequest) (*MinRentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMinRent not implemented")
}
func (UnimplementedWalletServiceServer) SendTx(context.Context, *SendTxRequest) (*SendTxResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendTx not implemented")
}
func (UnimplementedWalletServiceServer) GetTxByAddress(context.Context, *TxAddressRequest) (*TxAddressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTxByAddress not implemented")
}
func (UnimplementedWalletServiceServer) GetTxByHash(context.Context, *TxHashRequest) (*TxHashResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTxByHash not implemented")
}
func (UnimplementedWalletServiceServer) GetUtxo(context.Context, *UtxoRequest) (*UtxoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUtxo not implemented")
}
func (UnimplementedWalletServiceServer) GetUnspentOutputs(context.Context, *UnspentOutputsRequest) (*UnspentOutputsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUnspentOutputs not implemented")
}
func (UnimplementedWalletServiceServer) GetUtxoInsFromData(context.Context, *UtxoInsFromDataRequest) (*UtxoInsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUtxoInsFromData not implemented")
}
func (UnimplementedWalletServiceServer) GetAccountTxFromData(context.Context, *TxFromDataRequest) (*AccountTxResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountTxFromData not implemented")
}
func (UnimplementedWalletServiceServer) GetUtxoTxFromData(context.Context, *TxFromDataRequest) (*UtxoTxResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUtxoTxFromData not implemented")
}
func (UnimplementedWalletServiceServer) GetAccountTxFromSignedData(context.Context, *TxFromSignedDataRequest) (*AccountTxResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountTxFromSignedData not implemented")
}
func (UnimplementedWalletServiceServer) GetUtxoTxFromSignedData(context.Context, *TxFromSignedDataRequest) (*UtxoTxResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUtxoTxFromSignedData not implemented")
}
func (UnimplementedWalletServiceServer) CreateAccountSignedTx(context.Context, *CreateAccountSignedTxRequest) (*CreateSignedTxResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAccountSignedTx not implemented")
}
func (UnimplementedWalletServiceServer) CreateAccountTx(context.Context, *CreateAccountTxRequest) (*CreateAccountTxResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAccountTx not implemented")
}
func (UnimplementedWalletServiceServer) CreateUtxoSignedTx(context.Context, *CreateUtxoSignedTxRequest) (*CreateSignedTxResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUtxoSignedTx not implemented")
}
func (UnimplementedWalletServiceServer) CreateUtxoTx(context.Context, *CreateUtxoTxRequest) (*CreateUtxoTxResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUtxoTx not implemented")
}
func (UnimplementedWalletServiceServer) VerifyAccountSignedTx(context.Context, *VerifySignedTxRequest) (*VerifySignedTxResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyAccountSignedTx not implemented")
}
func (UnimplementedWalletServiceServer) VerifyUtxoSignedTx(context.Context, *VerifySignedTxRequest) (*VerifySignedTxResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyUtxoSignedTx not implemented")
}
func (UnimplementedWalletServiceServer) ABIBinToJSON(context.Context, *ABIBinToJSONRequest) (*ABIBinToJSONResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ABIBinToJSON not implemented")
}
func (UnimplementedWalletServiceServer) ABIJSONToBin(context.Context, *ABIJSONToBinRequest) (*ABIJSONToBinResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ABIJSONToBin not implemented")
}
func (UnimplementedWalletServiceServer) GetBlockByNumber(context.Context, *BlockInfoRequest) (*BlockInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlockByNumber not implemented")
}
func (UnimplementedWalletServiceServer) GetLatestSafeBlockHeader(context.Context, *BasicRequest) (*BlockHeaderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLatestSafeBlockHeader not implemented")
}
func (UnimplementedWalletServiceServer) GetLatestFinalizedBlockHeader(context.Context, *BasicRequest) (*BlockHeaderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLatestFinalizedBlockHeader not implemented")
}
func (UnimplementedWalletServiceServer) GetBlockHeaderByHash(context.Context, *BlockHeaderByHashRequest) (*BlockHeaderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlockHeaderByHash not implemented")
}
func (UnimplementedWalletServiceServer) GetBlockByRange(context.Context, *BlockByRangeRequest) (*BlockByRangeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlockByRange not implemented")
}
func (UnimplementedWalletServiceServer) GetTxReceiptByHash(context.Context, *TxReceiptByHashRequest) (*TxReceiptByHashResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTxReceiptByHash not implemented")
}
func (UnimplementedWalletServiceServer) GetStorageHash(context.Context, *StorageHashRequest) (*StorageHashResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStorageHash not implemented")
}
func (UnimplementedWalletServiceServer) GetFilterLogs(context.Context, *FilterLogsRequest) (*FilterLogsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFilterLogs not implemented")
}
func (UnimplementedWalletServiceServer) GetTxCountByAddress(context.Context, *TxCountByAddressRequest) (*TxCountByAddressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTxCountByAddress not implemented")
}
func (UnimplementedWalletServiceServer) GetSuggestGasPrice(context.Context, *SuggestGasPriceRequest) (*SuggestGasPriceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSuggestGasPrice not implemented")
}
func (UnimplementedWalletServiceServer) GetSuggestGasTipCap(context.Context, *SuggestGasPriceRequest) (*SuggestGasPriceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSuggestGasTipCap not implemented")
}
func (UnimplementedWalletServiceServer) testEmbeddedByValue() {}

// UnsafeWalletServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WalletServiceServer will
// result in compilation errors.
type UnsafeWalletServiceServer interface {
	mustEmbedUnimplementedWalletServiceServer()
}

func RegisterWalletServiceServer(s grpc.ServiceRegistrar, srv WalletServiceServer) {
	// If the following call pancis, it indicates UnimplementedWalletServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&WalletService_ServiceDesc, srv)
}

func _WalletService_GetSupportCoins_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SupportCoinsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetSupportCoins(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_GetSupportCoins_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetSupportCoins(ctx, req.(*SupportCoinsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_ConvertAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConvertAddressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).ConvertAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_ConvertAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).ConvertAddress(ctx, req.(*ConvertAddressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_ValidAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidAddressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).ValidAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_ValidAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).ValidAddress(ctx, req.(*ValidAddressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_GetBlock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetBlock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_GetBlock_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetBlock(ctx, req.(*BlockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_GetBlockHeaderByNumber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlockHeaderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetBlockHeaderByNumber(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_GetBlockHeaderByNumber_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetBlockHeaderByNumber(ctx, req.(*BlockHeaderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_GetNonce_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NonceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetNonce(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_GetNonce_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetNonce(ctx, req.(*NonceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_GetGasPrice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GasPriceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetGasPrice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_GetGasPrice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetGasPrice(ctx, req.(*GasPriceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_GetBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BalanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_GetBalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetBalance(ctx, req.(*BalanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_GetAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_GetAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetAccount(ctx, req.(*AccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_GetMinRent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MinRentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetMinRent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_GetMinRent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetMinRent(ctx, req.(*MinRentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_SendTx_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendTxRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).SendTx(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_SendTx_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).SendTx(ctx, req.(*SendTxRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_GetTxByAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TxAddressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetTxByAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_GetTxByAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetTxByAddress(ctx, req.(*TxAddressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_GetTxByHash_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TxHashRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetTxByHash(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_GetTxByHash_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetTxByHash(ctx, req.(*TxHashRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_GetUtxo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UtxoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetUtxo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_GetUtxo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetUtxo(ctx, req.(*UtxoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_GetUnspentOutputs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnspentOutputsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetUnspentOutputs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_GetUnspentOutputs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetUnspentOutputs(ctx, req.(*UnspentOutputsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_GetUtxoInsFromData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UtxoInsFromDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetUtxoInsFromData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_GetUtxoInsFromData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetUtxoInsFromData(ctx, req.(*UtxoInsFromDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_GetAccountTxFromData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TxFromDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetAccountTxFromData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_GetAccountTxFromData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetAccountTxFromData(ctx, req.(*TxFromDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_GetUtxoTxFromData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TxFromDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetUtxoTxFromData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_GetUtxoTxFromData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetUtxoTxFromData(ctx, req.(*TxFromDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_GetAccountTxFromSignedData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TxFromSignedDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetAccountTxFromSignedData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_GetAccountTxFromSignedData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetAccountTxFromSignedData(ctx, req.(*TxFromSignedDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_GetUtxoTxFromSignedData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TxFromSignedDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetUtxoTxFromSignedData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_GetUtxoTxFromSignedData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetUtxoTxFromSignedData(ctx, req.(*TxFromSignedDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_CreateAccountSignedTx_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAccountSignedTxRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).CreateAccountSignedTx(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_CreateAccountSignedTx_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).CreateAccountSignedTx(ctx, req.(*CreateAccountSignedTxRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_CreateAccountTx_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAccountTxRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).CreateAccountTx(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_CreateAccountTx_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).CreateAccountTx(ctx, req.(*CreateAccountTxRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_CreateUtxoSignedTx_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUtxoSignedTxRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).CreateUtxoSignedTx(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_CreateUtxoSignedTx_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).CreateUtxoSignedTx(ctx, req.(*CreateUtxoSignedTxRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_CreateUtxoTx_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUtxoTxRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).CreateUtxoTx(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_CreateUtxoTx_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).CreateUtxoTx(ctx, req.(*CreateUtxoTxRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_VerifyAccountSignedTx_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifySignedTxRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).VerifyAccountSignedTx(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_VerifyAccountSignedTx_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).VerifyAccountSignedTx(ctx, req.(*VerifySignedTxRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_VerifyUtxoSignedTx_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifySignedTxRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).VerifyUtxoSignedTx(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_VerifyUtxoSignedTx_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).VerifyUtxoSignedTx(ctx, req.(*VerifySignedTxRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_ABIBinToJSON_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ABIBinToJSONRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).ABIBinToJSON(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_ABIBinToJSON_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).ABIBinToJSON(ctx, req.(*ABIBinToJSONRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_ABIJSONToBin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ABIJSONToBinRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).ABIJSONToBin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_ABIJSONToBin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).ABIJSONToBin(ctx, req.(*ABIJSONToBinRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_GetBlockByNumber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlockInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetBlockByNumber(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_GetBlockByNumber_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetBlockByNumber(ctx, req.(*BlockInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_GetLatestSafeBlockHeader_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BasicRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetLatestSafeBlockHeader(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_GetLatestSafeBlockHeader_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetLatestSafeBlockHeader(ctx, req.(*BasicRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_GetLatestFinalizedBlockHeader_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BasicRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetLatestFinalizedBlockHeader(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_GetLatestFinalizedBlockHeader_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetLatestFinalizedBlockHeader(ctx, req.(*BasicRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_GetBlockHeaderByHash_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlockHeaderByHashRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetBlockHeaderByHash(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_GetBlockHeaderByHash_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetBlockHeaderByHash(ctx, req.(*BlockHeaderByHashRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_GetBlockByRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlockByRangeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetBlockByRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_GetBlockByRange_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetBlockByRange(ctx, req.(*BlockByRangeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_GetTxReceiptByHash_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TxReceiptByHashRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetTxReceiptByHash(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_GetTxReceiptByHash_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetTxReceiptByHash(ctx, req.(*TxReceiptByHashRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_GetStorageHash_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StorageHashRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetStorageHash(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_GetStorageHash_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetStorageHash(ctx, req.(*StorageHashRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_GetFilterLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FilterLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetFilterLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_GetFilterLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetFilterLogs(ctx, req.(*FilterLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_GetTxCountByAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TxCountByAddressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetTxCountByAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_GetTxCountByAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetTxCountByAddress(ctx, req.(*TxCountByAddressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_GetSuggestGasPrice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SuggestGasPriceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetSuggestGasPrice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_GetSuggestGasPrice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetSuggestGasPrice(ctx, req.(*SuggestGasPriceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletService_GetSuggestGasTipCap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SuggestGasPriceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServiceServer).GetSuggestGasTipCap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletService_GetSuggestGasTipCap_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServiceServer).GetSuggestGasTipCap(ctx, req.(*SuggestGasPriceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// WalletService_ServiceDesc is the grpc.ServiceDesc for WalletService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WalletService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "dapplink.wallet.WalletService",
	HandlerType: (*WalletServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "getSupportCoins",
			Handler:    _WalletService_GetSupportCoins_Handler,
		},
		{
			MethodName: "convertAddress",
			Handler:    _WalletService_ConvertAddress_Handler,
		},
		{
			MethodName: "validAddress",
			Handler:    _WalletService_ValidAddress_Handler,
		},
		{
			MethodName: "getBlock",
			Handler:    _WalletService_GetBlock_Handler,
		},
		{
			MethodName: "getBlockHeaderByNumber",
			Handler:    _WalletService_GetBlockHeaderByNumber_Handler,
		},
		{
			MethodName: "getNonce",
			Handler:    _WalletService_GetNonce_Handler,
		},
		{
			MethodName: "getGasPrice",
			Handler:    _WalletService_GetGasPrice_Handler,
		},
		{
			MethodName: "getBalance",
			Handler:    _WalletService_GetBalance_Handler,
		},
		{
			MethodName: "getAccount",
			Handler:    _WalletService_GetAccount_Handler,
		},
		{
			MethodName: "getMinRent",
			Handler:    _WalletService_GetMinRent_Handler,
		},
		{
			MethodName: "SendTx",
			Handler:    _WalletService_SendTx_Handler,
		},
		{
			MethodName: "getTxByAddress",
			Handler:    _WalletService_GetTxByAddress_Handler,
		},
		{
			MethodName: "getTxByHash",
			Handler:    _WalletService_GetTxByHash_Handler,
		},
		{
			MethodName: "getUtxo",
			Handler:    _WalletService_GetUtxo_Handler,
		},
		{
			MethodName: "getUnspentOutputs",
			Handler:    _WalletService_GetUnspentOutputs_Handler,
		},
		{
			MethodName: "getUtxoInsFromData",
			Handler:    _WalletService_GetUtxoInsFromData_Handler,
		},
		{
			MethodName: "getAccountTxFromData",
			Handler:    _WalletService_GetAccountTxFromData_Handler,
		},
		{
			MethodName: "getUtxoTxFromData",
			Handler:    _WalletService_GetUtxoTxFromData_Handler,
		},
		{
			MethodName: "getAccountTxFromSignedData",
			Handler:    _WalletService_GetAccountTxFromSignedData_Handler,
		},
		{
			MethodName: "GetUtxoTxFromSignedData",
			Handler:    _WalletService_GetUtxoTxFromSignedData_Handler,
		},
		{
			MethodName: "createAccountSignedTx",
			Handler:    _WalletService_CreateAccountSignedTx_Handler,
		},
		{
			MethodName: "createAccountTx",
			Handler:    _WalletService_CreateAccountTx_Handler,
		},
		{
			MethodName: "createUtxoSignedTx",
			Handler:    _WalletService_CreateUtxoSignedTx_Handler,
		},
		{
			MethodName: "createUtxoTx",
			Handler:    _WalletService_CreateUtxoTx_Handler,
		},
		{
			MethodName: "verifyAccountSignedTx",
			Handler:    _WalletService_VerifyAccountSignedTx_Handler,
		},
		{
			MethodName: "verifyUtxoSignedTx",
			Handler:    _WalletService_VerifyUtxoSignedTx_Handler,
		},
		{
			MethodName: "ABIBinToJSON",
			Handler:    _WalletService_ABIBinToJSON_Handler,
		},
		{
			MethodName: "ABIJSONToBin",
			Handler:    _WalletService_ABIJSONToBin_Handler,
		},
		{
			MethodName: "getBlockByNumber",
			Handler:    _WalletService_GetBlockByNumber_Handler,
		},
		{
			MethodName: "getLatestSafeBlockHeader",
			Handler:    _WalletService_GetLatestSafeBlockHeader_Handler,
		},
		{
			MethodName: "getLatestFinalizedBlockHeader",
			Handler:    _WalletService_GetLatestFinalizedBlockHeader_Handler,
		},
		{
			MethodName: "getBlockHeaderByHash",
			Handler:    _WalletService_GetBlockHeaderByHash_Handler,
		},
		{
			MethodName: "GetBlockByRange",
			Handler:    _WalletService_GetBlockByRange_Handler,
		},
		{
			MethodName: "getTxReceiptByHash",
			Handler:    _WalletService_GetTxReceiptByHash_Handler,
		},
		{
			MethodName: "getStorageHash",
			Handler:    _WalletService_GetStorageHash_Handler,
		},
		{
			MethodName: "getFilterLogs",
			Handler:    _WalletService_GetFilterLogs_Handler,
		},
		{
			MethodName: "getTxCountByAddress",
			Handler:    _WalletService_GetTxCountByAddress_Handler,
		},
		{
			MethodName: "getSuggestGasPrice",
			Handler:    _WalletService_GetSuggestGasPrice_Handler,
		},
		{
			MethodName: "getSuggestGasTipCap",
			Handler:    _WalletService_GetSuggestGasTipCap_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "dapplink/wallet.proto",
}
