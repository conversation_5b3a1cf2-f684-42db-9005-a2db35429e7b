// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v4.25.3
// source: dapplink/wallet.proto

package wallet

import (
	common "./proto/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TxStatus int32

const (
	TxStatus_NotFound              TxStatus = 0
	TxStatus_Pending               TxStatus = 1
	TxStatus_Failed                TxStatus = 2
	TxStatus_Success               TxStatus = 3
	TxStatus_ContractExecuteFailed TxStatus = 4
	TxStatus_Other                 TxStatus = 5
)

// Enum value maps for TxStatus.
var (
	TxStatus_name = map[int32]string{
		0: "NotFound",
		1: "Pending",
		2: "Failed",
		3: "Success",
		4: "ContractExecuteFailed",
		5: "Other",
	}
	TxStatus_value = map[string]int32{
		"NotFound":              0,
		"Pending":               1,
		"Failed":                2,
		"Success":               3,
		"ContractExecuteFailed": 4,
		"Other":                 5,
	}
)

func (x TxStatus) Enum() *TxStatus {
	p := new(TxStatus)
	*p = x
	return p
}

func (x TxStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TxStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_dapplink_wallet_proto_enumTypes[0].Descriptor()
}

func (TxStatus) Type() protoreflect.EnumType {
	return &file_dapplink_wallet_proto_enumTypes[0]
}

func (x TxStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TxStatus.Descriptor instead.
func (TxStatus) EnumDescriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{0}
}

type Address struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Address       string                 `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Address) Reset() {
	*x = Address{}
	mi := &file_dapplink_wallet_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Address) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Address) ProtoMessage() {}

func (x *Address) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Address.ProtoReflect.Descriptor instead.
func (*Address) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{0}
}

func (x *Address) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type Value struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Value         string                 `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Value) Reset() {
	*x = Value{}
	mi := &file_dapplink_wallet_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Value) ProtoMessage() {}

func (x *Value) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Value.ProtoReflect.Descriptor instead.
func (*Value) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{1}
}

func (x *Value) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type TxMessage struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Hash            string                 `protobuf:"bytes,1,opt,name=hash,proto3" json:"hash,omitempty"`
	Index           uint32                 `protobuf:"varint,2,opt,name=index,proto3" json:"index,omitempty"`
	Froms           []*Address             `protobuf:"bytes,3,rep,name=froms,proto3" json:"froms,omitempty"`
	Tos             []*Address             `protobuf:"bytes,4,rep,name=tos,proto3" json:"tos,omitempty"`
	Values          []*Value               `protobuf:"bytes,7,rep,name=values,proto3" json:"values,omitempty"`
	Fee             string                 `protobuf:"bytes,5,opt,name=fee,proto3" json:"fee,omitempty"`
	Status          TxStatus               `protobuf:"varint,6,opt,name=status,proto3,enum=dapplink.wallet.TxStatus" json:"status,omitempty"`
	Type            int32                  `protobuf:"varint,8,opt,name=type,proto3" json:"type,omitempty"`
	Height          string                 `protobuf:"bytes,9,opt,name=height,proto3" json:"height,omitempty"`
	ContractAddress string                 `protobuf:"bytes,10,opt,name=contract_address,json=contractAddress,proto3" json:"contract_address,omitempty"`
	Datetime        string                 `protobuf:"bytes,11,opt,name=datetime,proto3" json:"datetime,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *TxMessage) Reset() {
	*x = TxMessage{}
	mi := &file_dapplink_wallet_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TxMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxMessage) ProtoMessage() {}

func (x *TxMessage) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxMessage.ProtoReflect.Descriptor instead.
func (*TxMessage) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{2}
}

func (x *TxMessage) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *TxMessage) GetIndex() uint32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *TxMessage) GetFroms() []*Address {
	if x != nil {
		return x.Froms
	}
	return nil
}

func (x *TxMessage) GetTos() []*Address {
	if x != nil {
		return x.Tos
	}
	return nil
}

func (x *TxMessage) GetValues() []*Value {
	if x != nil {
		return x.Values
	}
	return nil
}

func (x *TxMessage) GetFee() string {
	if x != nil {
		return x.Fee
	}
	return ""
}

func (x *TxMessage) GetStatus() TxStatus {
	if x != nil {
		return x.Status
	}
	return TxStatus_NotFound
}

func (x *TxMessage) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *TxMessage) GetHeight() string {
	if x != nil {
		return x.Height
	}
	return ""
}

func (x *TxMessage) GetContractAddress() string {
	if x != nil {
		return x.ContractAddress
	}
	return ""
}

func (x *TxMessage) GetDatetime() string {
	if x != nil {
		return x.Datetime
	}
	return ""
}

type Vin struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Hash          string                 `protobuf:"bytes,1,opt,name=hash,proto3" json:"hash,omitempty"`
	Index         uint32                 `protobuf:"varint,2,opt,name=index,proto3" json:"index,omitempty"`
	Amount        int64                  `protobuf:"varint,3,opt,name=amount,proto3" json:"amount,omitempty"`
	Address       string                 `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Vin) Reset() {
	*x = Vin{}
	mi := &file_dapplink_wallet_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Vin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Vin) ProtoMessage() {}

func (x *Vin) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Vin.ProtoReflect.Descriptor instead.
func (*Vin) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{3}
}

func (x *Vin) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *Vin) GetIndex() uint32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *Vin) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *Vin) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type Vout struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Address       string                 `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	Amount        int64                  `protobuf:"varint,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Index         uint32                 `protobuf:"varint,3,opt,name=index,proto3" json:"index,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Vout) Reset() {
	*x = Vout{}
	mi := &file_dapplink_wallet_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Vout) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Vout) ProtoMessage() {}

func (x *Vout) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Vout.ProtoReflect.Descriptor instead.
func (*Vout) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{4}
}

func (x *Vout) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *Vout) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *Vout) GetIndex() uint32 {
	if x != nil {
		return x.Index
	}
	return 0
}

type SupportCoinsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SupportCoinsRequest) Reset() {
	*x = SupportCoinsRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SupportCoinsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SupportCoinsRequest) ProtoMessage() {}

func (x *SupportCoinsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SupportCoinsRequest.ProtoReflect.Descriptor instead.
func (*SupportCoinsRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{5}
}

func (x *SupportCoinsRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *SupportCoinsRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *SupportCoinsRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

type SupportCoinsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Support       bool                   `protobuf:"varint,3,opt,name=support,proto3" json:"support,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SupportCoinsResponse) Reset() {
	*x = SupportCoinsResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SupportCoinsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SupportCoinsResponse) ProtoMessage() {}

func (x *SupportCoinsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SupportCoinsResponse.ProtoReflect.Descriptor instead.
func (*SupportCoinsResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{6}
}

func (x *SupportCoinsResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *SupportCoinsResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SupportCoinsResponse) GetSupport() bool {
	if x != nil {
		return x.Support
	}
	return false
}

type ConvertAddressRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
	PublicKey     []byte                 `protobuf:"bytes,4,opt,name=public_key,json=publicKey,proto3" json:"public_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConvertAddressRequest) Reset() {
	*x = ConvertAddressRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConvertAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConvertAddressRequest) ProtoMessage() {}

func (x *ConvertAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConvertAddressRequest.ProtoReflect.Descriptor instead.
func (*ConvertAddressRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{7}
}

func (x *ConvertAddressRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *ConvertAddressRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *ConvertAddressRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *ConvertAddressRequest) GetPublicKey() []byte {
	if x != nil {
		return x.PublicKey
	}
	return nil
}

type ConvertAddressResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Address       string                 `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConvertAddressResponse) Reset() {
	*x = ConvertAddressResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConvertAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConvertAddressResponse) ProtoMessage() {}

func (x *ConvertAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConvertAddressResponse.ProtoReflect.Descriptor instead.
func (*ConvertAddressResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{8}
}

func (x *ConvertAddressResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *ConvertAddressResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ConvertAddressResponse) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type ValidAddressRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
	Symbol        string                 `protobuf:"bytes,4,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Address       string                 `protobuf:"bytes,5,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidAddressRequest) Reset() {
	*x = ValidAddressRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidAddressRequest) ProtoMessage() {}

func (x *ValidAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidAddressRequest.ProtoReflect.Descriptor instead.
func (*ValidAddressRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{9}
}

func (x *ValidAddressRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *ValidAddressRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *ValidAddressRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *ValidAddressRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *ValidAddressRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type ValidAddressResponse struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Code             common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg              string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Valid            bool                   `protobuf:"varint,3,opt,name=valid,proto3" json:"valid,omitempty"`
	CanWithdrawal    bool                   `protobuf:"varint,4,opt,name=can_withdrawal,json=canWithdrawal,proto3" json:"can_withdrawal,omitempty"`
	CanonicalAddress string                 `protobuf:"bytes,5,opt,name=canonical_address,json=canonicalAddress,proto3" json:"canonical_address,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ValidAddressResponse) Reset() {
	*x = ValidAddressResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidAddressResponse) ProtoMessage() {}

func (x *ValidAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidAddressResponse.ProtoReflect.Descriptor instead.
func (*ValidAddressResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{10}
}

func (x *ValidAddressResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *ValidAddressResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ValidAddressResponse) GetValid() bool {
	if x != nil {
		return x.Valid
	}
	return false
}

func (x *ValidAddressResponse) GetCanWithdrawal() bool {
	if x != nil {
		return x.CanWithdrawal
	}
	return false
}

func (x *ValidAddressResponse) GetCanonicalAddress() string {
	if x != nil {
		return x.CanonicalAddress
	}
	return ""
}

type NonceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Coin          string                 `protobuf:"bytes,3,opt,name=coin,proto3" json:"coin,omitempty"`
	Address       string                 `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	Network       string                 `protobuf:"bytes,5,opt,name=network,proto3" json:"network,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NonceRequest) Reset() {
	*x = NonceRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NonceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NonceRequest) ProtoMessage() {}

func (x *NonceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NonceRequest.ProtoReflect.Descriptor instead.
func (*NonceRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{11}
}

func (x *NonceRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *NonceRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *NonceRequest) GetCoin() string {
	if x != nil {
		return x.Coin
	}
	return ""
}

func (x *NonceRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *NonceRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

type NonceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Nonce         string                 `protobuf:"bytes,3,opt,name=nonce,proto3" json:"nonce,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NonceResponse) Reset() {
	*x = NonceResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NonceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NonceResponse) ProtoMessage() {}

func (x *NonceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NonceResponse.ProtoReflect.Descriptor instead.
func (*NonceResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{12}
}

func (x *NonceResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *NonceResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *NonceResponse) GetNonce() string {
	if x != nil {
		return x.Nonce
	}
	return ""
}

type GasPriceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Coin          string                 `protobuf:"bytes,3,opt,name=coin,proto3" json:"coin,omitempty"`
	Network       string                 `protobuf:"bytes,4,opt,name=network,proto3" json:"network,omitempty"`
	RawTx         string                 `protobuf:"bytes,5,opt,name=rawTx,proto3" json:"rawTx,omitempty"`
	Address       string                 `protobuf:"bytes,6,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GasPriceRequest) Reset() {
	*x = GasPriceRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GasPriceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GasPriceRequest) ProtoMessage() {}

func (x *GasPriceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GasPriceRequest.ProtoReflect.Descriptor instead.
func (*GasPriceRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{13}
}

func (x *GasPriceRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *GasPriceRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *GasPriceRequest) GetCoin() string {
	if x != nil {
		return x.Coin
	}
	return ""
}

func (x *GasPriceRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *GasPriceRequest) GetRawTx() string {
	if x != nil {
		return x.RawTx
	}
	return ""
}

func (x *GasPriceRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type GasPriceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Gas           string                 `protobuf:"bytes,3,opt,name=gas,proto3" json:"gas,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GasPriceResponse) Reset() {
	*x = GasPriceResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GasPriceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GasPriceResponse) ProtoMessage() {}

func (x *GasPriceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GasPriceResponse.ProtoReflect.Descriptor instead.
func (*GasPriceResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{14}
}

func (x *GasPriceResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *GasPriceResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GasPriceResponse) GetGas() string {
	if x != nil {
		return x.Gas
	}
	return ""
}

type BalanceRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken    string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain            string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Coin             string                 `protobuf:"bytes,3,opt,name=coin,proto3" json:"coin,omitempty"`
	Network          string                 `protobuf:"bytes,4,opt,name=network,proto3" json:"network,omitempty"`
	Address          string                 `protobuf:"bytes,5,opt,name=address,proto3" json:"address,omitempty"`
	ContractAddress  string                 `protobuf:"bytes,6,opt,name=contract_address,json=contractAddress,proto3" json:"contract_address,omitempty"`
	ProposerKeyIndex uint64                 `protobuf:"varint,7,opt,name=proposerKeyIndex,proto3" json:"proposerKeyIndex,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *BalanceRequest) Reset() {
	*x = BalanceRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BalanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BalanceRequest) ProtoMessage() {}

func (x *BalanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BalanceRequest.ProtoReflect.Descriptor instead.
func (*BalanceRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{15}
}

func (x *BalanceRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *BalanceRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *BalanceRequest) GetCoin() string {
	if x != nil {
		return x.Coin
	}
	return ""
}

func (x *BalanceRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *BalanceRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *BalanceRequest) GetContractAddress() string {
	if x != nil {
		return x.ContractAddress
	}
	return ""
}

func (x *BalanceRequest) GetProposerKeyIndex() uint64 {
	if x != nil {
		return x.ProposerKeyIndex
	}
	return 0
}

type BalanceResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Code           common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg            string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Balance        string                 `protobuf:"bytes,3,opt,name=balance,proto3" json:"balance,omitempty"`
	SequenceNumber uint64                 `protobuf:"varint,4,opt,name=sequenceNumber,proto3" json:"sequenceNumber,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BalanceResponse) Reset() {
	*x = BalanceResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BalanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BalanceResponse) ProtoMessage() {}

func (x *BalanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BalanceResponse.ProtoReflect.Descriptor instead.
func (*BalanceResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{16}
}

func (x *BalanceResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *BalanceResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *BalanceResponse) GetBalance() string {
	if x != nil {
		return x.Balance
	}
	return ""
}

func (x *BalanceResponse) GetSequenceNumber() uint64 {
	if x != nil {
		return x.SequenceNumber
	}
	return 0
}

type AccountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Coin          string                 `protobuf:"bytes,3,opt,name=coin,proto3" json:"coin,omitempty"`
	Network       string                 `protobuf:"bytes,4,opt,name=network,proto3" json:"network,omitempty"`
	Address       string                 `protobuf:"bytes,5,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountRequest) Reset() {
	*x = AccountRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountRequest) ProtoMessage() {}

func (x *AccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountRequest.ProtoReflect.Descriptor instead.
func (*AccountRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{17}
}

func (x *AccountRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *AccountRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *AccountRequest) GetCoin() string {
	if x != nil {
		return x.Coin
	}
	return ""
}

func (x *AccountRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *AccountRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type AccountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	AccountNumber string                 `protobuf:"bytes,3,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	Sequence      string                 `protobuf:"bytes,4,opt,name=sequence,proto3" json:"sequence,omitempty"`
	Network       string                 `protobuf:"bytes,5,opt,name=network,proto3" json:"network,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountResponse) Reset() {
	*x = AccountResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountResponse) ProtoMessage() {}

func (x *AccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountResponse.ProtoReflect.Descriptor instead.
func (*AccountResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{18}
}

func (x *AccountResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *AccountResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AccountResponse) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *AccountResponse) GetSequence() string {
	if x != nil {
		return x.Sequence
	}
	return ""
}

func (x *AccountResponse) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

type MinRentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Coin          string                 `protobuf:"bytes,3,opt,name=coin,proto3" json:"coin,omitempty"`
	Network       string                 `protobuf:"bytes,4,opt,name=network,proto3" json:"network,omitempty"`
	Address       string                 `protobuf:"bytes,5,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MinRentRequest) Reset() {
	*x = MinRentRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MinRentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MinRentRequest) ProtoMessage() {}

func (x *MinRentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MinRentRequest.ProtoReflect.Descriptor instead.
func (*MinRentRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{19}
}

func (x *MinRentRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *MinRentRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *MinRentRequest) GetCoin() string {
	if x != nil {
		return x.Coin
	}
	return ""
}

func (x *MinRentRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *MinRentRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type MinRentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Value         string                 `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MinRentResponse) Reset() {
	*x = MinRentResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MinRentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MinRentResponse) ProtoMessage() {}

func (x *MinRentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MinRentResponse.ProtoReflect.Descriptor instead.
func (*MinRentResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{20}
}

func (x *MinRentResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *MinRentResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *MinRentResponse) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type SendTxRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Coin          string                 `protobuf:"bytes,3,opt,name=coin,proto3" json:"coin,omitempty"`
	Network       string                 `protobuf:"bytes,4,opt,name=network,proto3" json:"network,omitempty"`
	RawTx         string                 `protobuf:"bytes,5,opt,name=raw_tx,json=rawTx,proto3" json:"raw_tx,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendTxRequest) Reset() {
	*x = SendTxRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendTxRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendTxRequest) ProtoMessage() {}

func (x *SendTxRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendTxRequest.ProtoReflect.Descriptor instead.
func (*SendTxRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{21}
}

func (x *SendTxRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *SendTxRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *SendTxRequest) GetCoin() string {
	if x != nil {
		return x.Coin
	}
	return ""
}

func (x *SendTxRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *SendTxRequest) GetRawTx() string {
	if x != nil {
		return x.RawTx
	}
	return ""
}

type SendTxResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	TxHash        string                 `protobuf:"bytes,3,opt,name=tx_hash,json=txHash,proto3" json:"tx_hash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendTxResponse) Reset() {
	*x = SendTxResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendTxResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendTxResponse) ProtoMessage() {}

func (x *SendTxResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendTxResponse.ProtoReflect.Descriptor instead.
func (*SendTxResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{22}
}

func (x *SendTxResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *SendTxResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SendTxResponse) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

type TxAddressRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken   string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain           string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Coin            string                 `protobuf:"bytes,3,opt,name=coin,proto3" json:"coin,omitempty"`
	Network         string                 `protobuf:"bytes,4,opt,name=network,proto3" json:"network,omitempty"`
	Address         string                 `protobuf:"bytes,5,opt,name=address,proto3" json:"address,omitempty"`
	ContractAddress string                 `protobuf:"bytes,6,opt,name=contract_address,json=contractAddress,proto3" json:"contract_address,omitempty"`
	Page            uint32                 `protobuf:"varint,7,opt,name=page,proto3" json:"page,omitempty"`
	Pagesize        uint32                 `protobuf:"varint,8,opt,name=pagesize,proto3" json:"pagesize,omitempty"`
	Cursor          string                 `protobuf:"bytes,9,opt,name=cursor,proto3" json:"cursor,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *TxAddressRequest) Reset() {
	*x = TxAddressRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TxAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxAddressRequest) ProtoMessage() {}

func (x *TxAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxAddressRequest.ProtoReflect.Descriptor instead.
func (*TxAddressRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{23}
}

func (x *TxAddressRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *TxAddressRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *TxAddressRequest) GetCoin() string {
	if x != nil {
		return x.Coin
	}
	return ""
}

func (x *TxAddressRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *TxAddressRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *TxAddressRequest) GetContractAddress() string {
	if x != nil {
		return x.ContractAddress
	}
	return ""
}

func (x *TxAddressRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *TxAddressRequest) GetPagesize() uint32 {
	if x != nil {
		return x.Pagesize
	}
	return 0
}

func (x *TxAddressRequest) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

type TxAddressResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Tx            []*TxMessage           `protobuf:"bytes,3,rep,name=tx,proto3" json:"tx,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TxAddressResponse) Reset() {
	*x = TxAddressResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TxAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxAddressResponse) ProtoMessage() {}

func (x *TxAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxAddressResponse.ProtoReflect.Descriptor instead.
func (*TxAddressResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{24}
}

func (x *TxAddressResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *TxAddressResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *TxAddressResponse) GetTx() []*TxMessage {
	if x != nil {
		return x.Tx
	}
	return nil
}

type TxHashRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Coin          string                 `protobuf:"bytes,3,opt,name=coin,proto3" json:"coin,omitempty"`
	Network       string                 `protobuf:"bytes,4,opt,name=network,proto3" json:"network,omitempty"`
	Hash          string                 `protobuf:"bytes,5,opt,name=hash,proto3" json:"hash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TxHashRequest) Reset() {
	*x = TxHashRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TxHashRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxHashRequest) ProtoMessage() {}

func (x *TxHashRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxHashRequest.ProtoReflect.Descriptor instead.
func (*TxHashRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{25}
}

func (x *TxHashRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *TxHashRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *TxHashRequest) GetCoin() string {
	if x != nil {
		return x.Coin
	}
	return ""
}

func (x *TxHashRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *TxHashRequest) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

type TxHashResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Tx            *TxMessage             `protobuf:"bytes,3,opt,name=tx,proto3" json:"tx,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TxHashResponse) Reset() {
	*x = TxHashResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TxHashResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxHashResponse) ProtoMessage() {}

func (x *TxHashResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxHashResponse.ProtoReflect.Descriptor instead.
func (*TxHashResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{26}
}

func (x *TxHashResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *TxHashResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *TxHashResponse) GetTx() *TxMessage {
	if x != nil {
		return x.Tx
	}
	return nil
}

type UtxoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Symbol        string                 `protobuf:"bytes,3,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Network       string                 `protobuf:"bytes,4,opt,name=network,proto3" json:"network,omitempty"`
	Vin           *Vin                   `protobuf:"bytes,5,opt,name=vin,proto3" json:"vin,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UtxoRequest) Reset() {
	*x = UtxoRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UtxoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UtxoRequest) ProtoMessage() {}

func (x *UtxoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UtxoRequest.ProtoReflect.Descriptor instead.
func (*UtxoRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{27}
}

func (x *UtxoRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *UtxoRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *UtxoRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *UtxoRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *UtxoRequest) GetVin() *Vin {
	if x != nil {
		return x.Vin
	}
	return nil
}

type UtxoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Unspent       bool                   `protobuf:"varint,3,opt,name=unspent,proto3" json:"unspent,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UtxoResponse) Reset() {
	*x = UtxoResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UtxoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UtxoResponse) ProtoMessage() {}

func (x *UtxoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UtxoResponse.ProtoReflect.Descriptor instead.
func (*UtxoResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{28}
}

func (x *UtxoResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *UtxoResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UtxoResponse) GetUnspent() bool {
	if x != nil {
		return x.Unspent
	}
	return false
}

type UnspentOutput struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	TxHashBigEndian string                 `protobuf:"bytes,1,opt,name=tx_hash_big_endian,json=txHashBigEndian,proto3" json:"tx_hash_big_endian,omitempty"`
	TxHash          string                 `protobuf:"bytes,2,opt,name=tx_hash,json=txHash,proto3" json:"tx_hash,omitempty"`
	TxOutputN       uint64                 `protobuf:"varint,3,opt,name=tx_output_n,json=txOutputN,proto3" json:"tx_output_n,omitempty"`
	Script          string                 `protobuf:"bytes,4,opt,name=script,proto3" json:"script,omitempty"`
	Value           uint64                 `protobuf:"varint,5,opt,name=value,proto3" json:"value,omitempty"`
	Confirmations   uint64                 `protobuf:"varint,6,opt,name=confirmations,proto3" json:"confirmations,omitempty"`
	TxIndex         uint64                 `protobuf:"varint,7,opt,name=tx_index,json=txIndex,proto3" json:"tx_index,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *UnspentOutput) Reset() {
	*x = UnspentOutput{}
	mi := &file_dapplink_wallet_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnspentOutput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnspentOutput) ProtoMessage() {}

func (x *UnspentOutput) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnspentOutput.ProtoReflect.Descriptor instead.
func (*UnspentOutput) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{29}
}

func (x *UnspentOutput) GetTxHashBigEndian() string {
	if x != nil {
		return x.TxHashBigEndian
	}
	return ""
}

func (x *UnspentOutput) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

func (x *UnspentOutput) GetTxOutputN() uint64 {
	if x != nil {
		return x.TxOutputN
	}
	return 0
}

func (x *UnspentOutput) GetScript() string {
	if x != nil {
		return x.Script
	}
	return ""
}

func (x *UnspentOutput) GetValue() uint64 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *UnspentOutput) GetConfirmations() uint64 {
	if x != nil {
		return x.Confirmations
	}
	return 0
}

func (x *UnspentOutput) GetTxIndex() uint64 {
	if x != nil {
		return x.TxIndex
	}
	return 0
}

type UnspentOutputsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Symbol        string                 `protobuf:"bytes,3,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Network       string                 `protobuf:"bytes,4,opt,name=network,proto3" json:"network,omitempty"`
	Address       string                 `protobuf:"bytes,5,opt,name=address,proto3" json:"address,omitempty"`
	Cursor        string                 `protobuf:"bytes,6,opt,name=cursor,proto3" json:"cursor,omitempty"`
	Limit         uint64                 `protobuf:"varint,7,opt,name=limit,proto3" json:"limit,omitempty"`
	CoinType      string                 `protobuf:"bytes,8,opt,name=coinType,proto3" json:"coinType,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnspentOutputsRequest) Reset() {
	*x = UnspentOutputsRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnspentOutputsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnspentOutputsRequest) ProtoMessage() {}

func (x *UnspentOutputsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnspentOutputsRequest.ProtoReflect.Descriptor instead.
func (*UnspentOutputsRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{30}
}

func (x *UnspentOutputsRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *UnspentOutputsRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *UnspentOutputsRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *UnspentOutputsRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *UnspentOutputsRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *UnspentOutputsRequest) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

func (x *UnspentOutputsRequest) GetLimit() uint64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *UnspentOutputsRequest) GetCoinType() string {
	if x != nil {
		return x.CoinType
	}
	return ""
}

type UnspentOutputsResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Code           common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg            string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	UnspentOutputs []*UnspentOutput       `protobuf:"bytes,3,rep,name=unspent_outputs,json=unspentOutputs,proto3" json:"unspent_outputs,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UnspentOutputsResponse) Reset() {
	*x = UnspentOutputsResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnspentOutputsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnspentOutputsResponse) ProtoMessage() {}

func (x *UnspentOutputsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnspentOutputsResponse.ProtoReflect.Descriptor instead.
func (*UnspentOutputsResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{31}
}

func (x *UnspentOutputsResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *UnspentOutputsResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UnspentOutputsResponse) GetUnspentOutputs() []*UnspentOutput {
	if x != nil {
		return x.UnspentOutputs
	}
	return nil
}

type UtxoInsFromDataRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Symbol        string                 `protobuf:"bytes,3,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Network       string                 `protobuf:"bytes,4,opt,name=network,proto3" json:"network,omitempty"`
	Data          []byte                 `protobuf:"bytes,5,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UtxoInsFromDataRequest) Reset() {
	*x = UtxoInsFromDataRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UtxoInsFromDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UtxoInsFromDataRequest) ProtoMessage() {}

func (x *UtxoInsFromDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UtxoInsFromDataRequest.ProtoReflect.Descriptor instead.
func (*UtxoInsFromDataRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{32}
}

func (x *UtxoInsFromDataRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *UtxoInsFromDataRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *UtxoInsFromDataRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *UtxoInsFromDataRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *UtxoInsFromDataRequest) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type UtxoInsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Vins          []*Vin                 `protobuf:"bytes,3,rep,name=vins,proto3" json:"vins,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UtxoInsResponse) Reset() {
	*x = UtxoInsResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UtxoInsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UtxoInsResponse) ProtoMessage() {}

func (x *UtxoInsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UtxoInsResponse.ProtoReflect.Descriptor instead.
func (*UtxoInsResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{33}
}

func (x *UtxoInsResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *UtxoInsResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UtxoInsResponse) GetVins() []*Vin {
	if x != nil {
		return x.Vins
	}
	return nil
}

type TxFromDataRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Symbol        string                 `protobuf:"bytes,3,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Network       string                 `protobuf:"bytes,4,opt,name=network,proto3" json:"network,omitempty"`
	RawData       []byte                 `protobuf:"bytes,5,opt,name=raw_data,json=rawData,proto3" json:"raw_data,omitempty"`
	Height        int64                  `protobuf:"varint,6,opt,name=height,proto3" json:"height,omitempty"`
	Vins          []*Vin                 `protobuf:"bytes,7,rep,name=vins,proto3" json:"vins,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TxFromDataRequest) Reset() {
	*x = TxFromDataRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TxFromDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxFromDataRequest) ProtoMessage() {}

func (x *TxFromDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxFromDataRequest.ProtoReflect.Descriptor instead.
func (*TxFromDataRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{34}
}

func (x *TxFromDataRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *TxFromDataRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *TxFromDataRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *TxFromDataRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *TxFromDataRequest) GetRawData() []byte {
	if x != nil {
		return x.RawData
	}
	return nil
}

func (x *TxFromDataRequest) GetHeight() int64 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *TxFromDataRequest) GetVins() []*Vin {
	if x != nil {
		return x.Vins
	}
	return nil
}

type AccountTxResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Code            common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg             string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	TxHash          string                 `protobuf:"bytes,3,opt,name=tx_hash,json=txHash,proto3" json:"tx_hash,omitempty"`
	Status          TxStatus               `protobuf:"varint,4,opt,name=status,proto3,enum=dapplink.wallet.TxStatus" json:"status,omitempty"`
	From            string                 `protobuf:"bytes,5,opt,name=from,proto3" json:"from,omitempty"`
	To              string                 `protobuf:"bytes,6,opt,name=to,proto3" json:"to,omitempty"`
	Amount          string                 `protobuf:"bytes,7,opt,name=amount,proto3" json:"amount,omitempty"`
	Memo            string                 `protobuf:"bytes,8,opt,name=memo,proto3" json:"memo,omitempty"`
	Nonce           uint64                 `protobuf:"varint,9,opt,name=nonce,proto3" json:"nonce,omitempty"`
	GasLimit        string                 `protobuf:"bytes,10,opt,name=gas_limit,json=gasLimit,proto3" json:"gas_limit,omitempty"`
	GasPrice        string                 `protobuf:"bytes,11,opt,name=gas_price,json=gasPrice,proto3" json:"gas_price,omitempty"`
	CostFee         string                 `protobuf:"bytes,12,opt,name=cost_fee,json=costFee,proto3" json:"cost_fee,omitempty"`
	BlockHeight     uint64                 `protobuf:"varint,13,opt,name=block_height,json=blockHeight,proto3" json:"block_height,omitempty"`
	BlockTime       uint64                 `protobuf:"varint,14,opt,name=block_time,json=blockTime,proto3" json:"block_time,omitempty"`
	SignHash        []byte                 `protobuf:"bytes,15,opt,name=sign_hash,json=signHash,proto3" json:"sign_hash,omitempty"`
	ContractAddress string                 `protobuf:"bytes,16,opt,name=contract_address,json=contractAddress,proto3" json:"contract_address,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *AccountTxResponse) Reset() {
	*x = AccountTxResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountTxResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountTxResponse) ProtoMessage() {}

func (x *AccountTxResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountTxResponse.ProtoReflect.Descriptor instead.
func (*AccountTxResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{35}
}

func (x *AccountTxResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *AccountTxResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AccountTxResponse) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

func (x *AccountTxResponse) GetStatus() TxStatus {
	if x != nil {
		return x.Status
	}
	return TxStatus_NotFound
}

func (x *AccountTxResponse) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *AccountTxResponse) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

func (x *AccountTxResponse) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *AccountTxResponse) GetMemo() string {
	if x != nil {
		return x.Memo
	}
	return ""
}

func (x *AccountTxResponse) GetNonce() uint64 {
	if x != nil {
		return x.Nonce
	}
	return 0
}

func (x *AccountTxResponse) GetGasLimit() string {
	if x != nil {
		return x.GasLimit
	}
	return ""
}

func (x *AccountTxResponse) GetGasPrice() string {
	if x != nil {
		return x.GasPrice
	}
	return ""
}

func (x *AccountTxResponse) GetCostFee() string {
	if x != nil {
		return x.CostFee
	}
	return ""
}

func (x *AccountTxResponse) GetBlockHeight() uint64 {
	if x != nil {
		return x.BlockHeight
	}
	return 0
}

func (x *AccountTxResponse) GetBlockTime() uint64 {
	if x != nil {
		return x.BlockTime
	}
	return 0
}

func (x *AccountTxResponse) GetSignHash() []byte {
	if x != nil {
		return x.SignHash
	}
	return nil
}

func (x *AccountTxResponse) GetContractAddress() string {
	if x != nil {
		return x.ContractAddress
	}
	return ""
}

type UtxoTxResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	TxHash        string                 `protobuf:"bytes,3,opt,name=tx_hash,json=txHash,proto3" json:"tx_hash,omitempty"`
	Status        TxStatus               `protobuf:"varint,4,opt,name=status,proto3,enum=dapplink.wallet.TxStatus" json:"status,omitempty"`
	Vins          []*Vin                 `protobuf:"bytes,5,rep,name=vins,proto3" json:"vins,omitempty"`
	Vouts         []*Vout                `protobuf:"bytes,6,rep,name=vouts,proto3" json:"vouts,omitempty"`
	SignHashes    [][]byte               `protobuf:"bytes,7,rep,name=sign_hashes,json=signHashes,proto3" json:"sign_hashes,omitempty"`
	CostFee       string                 `protobuf:"bytes,8,opt,name=cost_fee,json=costFee,proto3" json:"cost_fee,omitempty"`
	BlockHeight   uint64                 `protobuf:"varint,9,opt,name=block_height,json=blockHeight,proto3" json:"block_height,omitempty"`
	BlockTime     uint64                 `protobuf:"varint,10,opt,name=block_time,json=blockTime,proto3" json:"block_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UtxoTxResponse) Reset() {
	*x = UtxoTxResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UtxoTxResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UtxoTxResponse) ProtoMessage() {}

func (x *UtxoTxResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UtxoTxResponse.ProtoReflect.Descriptor instead.
func (*UtxoTxResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{36}
}

func (x *UtxoTxResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *UtxoTxResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UtxoTxResponse) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

func (x *UtxoTxResponse) GetStatus() TxStatus {
	if x != nil {
		return x.Status
	}
	return TxStatus_NotFound
}

func (x *UtxoTxResponse) GetVins() []*Vin {
	if x != nil {
		return x.Vins
	}
	return nil
}

func (x *UtxoTxResponse) GetVouts() []*Vout {
	if x != nil {
		return x.Vouts
	}
	return nil
}

func (x *UtxoTxResponse) GetSignHashes() [][]byte {
	if x != nil {
		return x.SignHashes
	}
	return nil
}

func (x *UtxoTxResponse) GetCostFee() string {
	if x != nil {
		return x.CostFee
	}
	return ""
}

func (x *UtxoTxResponse) GetBlockHeight() uint64 {
	if x != nil {
		return x.BlockHeight
	}
	return 0
}

func (x *UtxoTxResponse) GetBlockTime() uint64 {
	if x != nil {
		return x.BlockTime
	}
	return 0
}

type TxFromSignedDataRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Symbol        string                 `protobuf:"bytes,3,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Network       string                 `protobuf:"bytes,4,opt,name=network,proto3" json:"network,omitempty"`
	SignedTxData  []byte                 `protobuf:"bytes,5,opt,name=signed_tx_data,json=signedTxData,proto3" json:"signed_tx_data,omitempty"`
	Height        int64                  `protobuf:"varint,6,opt,name=height,proto3" json:"height,omitempty"`
	Vins          []*Vin                 `protobuf:"bytes,7,rep,name=vins,proto3" json:"vins,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TxFromSignedDataRequest) Reset() {
	*x = TxFromSignedDataRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TxFromSignedDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxFromSignedDataRequest) ProtoMessage() {}

func (x *TxFromSignedDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxFromSignedDataRequest.ProtoReflect.Descriptor instead.
func (*TxFromSignedDataRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{37}
}

func (x *TxFromSignedDataRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *TxFromSignedDataRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *TxFromSignedDataRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *TxFromSignedDataRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *TxFromSignedDataRequest) GetSignedTxData() []byte {
	if x != nil {
		return x.SignedTxData
	}
	return nil
}

func (x *TxFromSignedDataRequest) GetHeight() int64 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *TxFromSignedDataRequest) GetVins() []*Vin {
	if x != nil {
		return x.Vins
	}
	return nil
}

type CreateAccountSignedTxRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Symbol        string                 `protobuf:"bytes,3,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Network       string                 `protobuf:"bytes,4,opt,name=network,proto3" json:"network,omitempty"`
	TxData        []byte                 `protobuf:"bytes,5,opt,name=tx_data,json=txData,proto3" json:"tx_data,omitempty"`
	Signature     []byte                 `protobuf:"bytes,6,opt,name=signature,proto3" json:"signature,omitempty"`
	PublicKey     []byte                 `protobuf:"bytes,7,opt,name=public_key,json=publicKey,proto3" json:"public_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAccountSignedTxRequest) Reset() {
	*x = CreateAccountSignedTxRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAccountSignedTxRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAccountSignedTxRequest) ProtoMessage() {}

func (x *CreateAccountSignedTxRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAccountSignedTxRequest.ProtoReflect.Descriptor instead.
func (*CreateAccountSignedTxRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{38}
}

func (x *CreateAccountSignedTxRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *CreateAccountSignedTxRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *CreateAccountSignedTxRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *CreateAccountSignedTxRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *CreateAccountSignedTxRequest) GetTxData() []byte {
	if x != nil {
		return x.TxData
	}
	return nil
}

func (x *CreateAccountSignedTxRequest) GetSignature() []byte {
	if x != nil {
		return x.Signature
	}
	return nil
}

func (x *CreateAccountSignedTxRequest) GetPublicKey() []byte {
	if x != nil {
		return x.PublicKey
	}
	return nil
}

type CreateSignedTxResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	SignedTxData  []byte                 `protobuf:"bytes,3,opt,name=signed_tx_data,json=signedTxData,proto3" json:"signed_tx_data,omitempty"`
	Hash          []byte                 `protobuf:"bytes,4,opt,name=hash,proto3" json:"hash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateSignedTxResponse) Reset() {
	*x = CreateSignedTxResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateSignedTxResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSignedTxResponse) ProtoMessage() {}

func (x *CreateSignedTxResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSignedTxResponse.ProtoReflect.Descriptor instead.
func (*CreateSignedTxResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{39}
}

func (x *CreateSignedTxResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *CreateSignedTxResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CreateSignedTxResponse) GetSignedTxData() []byte {
	if x != nil {
		return x.SignedTxData
	}
	return nil
}

func (x *CreateSignedTxResponse) GetHash() []byte {
	if x != nil {
		return x.Hash
	}
	return nil
}

type CreateAccountTxRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken   string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain           string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Symbol          string                 `protobuf:"bytes,3,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Network         string                 `protobuf:"bytes,4,opt,name=network,proto3" json:"network,omitempty"`
	From            string                 `protobuf:"bytes,5,opt,name=from,proto3" json:"from,omitempty"`
	To              string                 `protobuf:"bytes,6,opt,name=to,proto3" json:"to,omitempty"`
	Amount          string                 `protobuf:"bytes,7,opt,name=amount,proto3" json:"amount,omitempty"`
	Memo            string                 `protobuf:"bytes,8,opt,name=memo,proto3" json:"memo,omitempty"`
	GasLimit        string                 `protobuf:"bytes,9,opt,name=gas_limit,json=gasLimit,proto3" json:"gas_limit,omitempty"`
	GasPrice        string                 `protobuf:"bytes,10,opt,name=gas_price,json=gasPrice,proto3" json:"gas_price,omitempty"`
	Nonce           uint64                 `protobuf:"varint,11,opt,name=nonce,proto3" json:"nonce,omitempty"`
	ContractAddress string                 `protobuf:"bytes,12,opt,name=contract_address,json=contractAddress,proto3" json:"contract_address,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CreateAccountTxRequest) Reset() {
	*x = CreateAccountTxRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAccountTxRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAccountTxRequest) ProtoMessage() {}

func (x *CreateAccountTxRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAccountTxRequest.ProtoReflect.Descriptor instead.
func (*CreateAccountTxRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{40}
}

func (x *CreateAccountTxRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *CreateAccountTxRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *CreateAccountTxRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *CreateAccountTxRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *CreateAccountTxRequest) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *CreateAccountTxRequest) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

func (x *CreateAccountTxRequest) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *CreateAccountTxRequest) GetMemo() string {
	if x != nil {
		return x.Memo
	}
	return ""
}

func (x *CreateAccountTxRequest) GetGasLimit() string {
	if x != nil {
		return x.GasLimit
	}
	return ""
}

func (x *CreateAccountTxRequest) GetGasPrice() string {
	if x != nil {
		return x.GasPrice
	}
	return ""
}

func (x *CreateAccountTxRequest) GetNonce() uint64 {
	if x != nil {
		return x.Nonce
	}
	return 0
}

func (x *CreateAccountTxRequest) GetContractAddress() string {
	if x != nil {
		return x.ContractAddress
	}
	return ""
}

type CreateAccountTxResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	TxData        []byte                 `protobuf:"bytes,3,opt,name=tx_data,json=txData,proto3" json:"tx_data,omitempty"`
	SignHash      []byte                 `protobuf:"bytes,4,opt,name=sign_hash,json=signHash,proto3" json:"sign_hash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAccountTxResponse) Reset() {
	*x = CreateAccountTxResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAccountTxResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAccountTxResponse) ProtoMessage() {}

func (x *CreateAccountTxResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAccountTxResponse.ProtoReflect.Descriptor instead.
func (*CreateAccountTxResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{41}
}

func (x *CreateAccountTxResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *CreateAccountTxResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CreateAccountTxResponse) GetTxData() []byte {
	if x != nil {
		return x.TxData
	}
	return nil
}

func (x *CreateAccountTxResponse) GetSignHash() []byte {
	if x != nil {
		return x.SignHash
	}
	return nil
}

type CreateUtxoSignedTxRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Symbol        string                 `protobuf:"bytes,3,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Network       string                 `protobuf:"bytes,4,opt,name=network,proto3" json:"network,omitempty"`
	TxData        []byte                 `protobuf:"bytes,5,opt,name=tx_data,json=txData,proto3" json:"tx_data,omitempty"`
	Signatures    [][]byte               `protobuf:"bytes,6,rep,name=signatures,proto3" json:"signatures,omitempty"`
	PublicKeys    [][]byte               `protobuf:"bytes,7,rep,name=public_keys,json=publicKeys,proto3" json:"public_keys,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUtxoSignedTxRequest) Reset() {
	*x = CreateUtxoSignedTxRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUtxoSignedTxRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUtxoSignedTxRequest) ProtoMessage() {}

func (x *CreateUtxoSignedTxRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUtxoSignedTxRequest.ProtoReflect.Descriptor instead.
func (*CreateUtxoSignedTxRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{42}
}

func (x *CreateUtxoSignedTxRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *CreateUtxoSignedTxRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *CreateUtxoSignedTxRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *CreateUtxoSignedTxRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *CreateUtxoSignedTxRequest) GetTxData() []byte {
	if x != nil {
		return x.TxData
	}
	return nil
}

func (x *CreateUtxoSignedTxRequest) GetSignatures() [][]byte {
	if x != nil {
		return x.Signatures
	}
	return nil
}

func (x *CreateUtxoSignedTxRequest) GetPublicKeys() [][]byte {
	if x != nil {
		return x.PublicKeys
	}
	return nil
}

type CreateUtxoTxRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Symbol        string                 `protobuf:"bytes,3,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Network       string                 `protobuf:"bytes,4,opt,name=network,proto3" json:"network,omitempty"`
	Vins          []*Vin                 `protobuf:"bytes,5,rep,name=vins,proto3" json:"vins,omitempty"`
	Vouts         []*Vout                `protobuf:"bytes,6,rep,name=vouts,proto3" json:"vouts,omitempty"`
	Fee           string                 `protobuf:"bytes,7,opt,name=fee,proto3" json:"fee,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUtxoTxRequest) Reset() {
	*x = CreateUtxoTxRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUtxoTxRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUtxoTxRequest) ProtoMessage() {}

func (x *CreateUtxoTxRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUtxoTxRequest.ProtoReflect.Descriptor instead.
func (*CreateUtxoTxRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{43}
}

func (x *CreateUtxoTxRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *CreateUtxoTxRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *CreateUtxoTxRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *CreateUtxoTxRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *CreateUtxoTxRequest) GetVins() []*Vin {
	if x != nil {
		return x.Vins
	}
	return nil
}

func (x *CreateUtxoTxRequest) GetVouts() []*Vout {
	if x != nil {
		return x.Vouts
	}
	return nil
}

func (x *CreateUtxoTxRequest) GetFee() string {
	if x != nil {
		return x.Fee
	}
	return ""
}

type CreateUtxoTxResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	TxData        []byte                 `protobuf:"bytes,3,opt,name=tx_data,json=txData,proto3" json:"tx_data,omitempty"`
	SignHashes    [][]byte               `protobuf:"bytes,4,rep,name=sign_hashes,json=signHashes,proto3" json:"sign_hashes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUtxoTxResponse) Reset() {
	*x = CreateUtxoTxResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUtxoTxResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUtxoTxResponse) ProtoMessage() {}

func (x *CreateUtxoTxResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUtxoTxResponse.ProtoReflect.Descriptor instead.
func (*CreateUtxoTxResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{44}
}

func (x *CreateUtxoTxResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *CreateUtxoTxResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CreateUtxoTxResponse) GetTxData() []byte {
	if x != nil {
		return x.TxData
	}
	return nil
}

func (x *CreateUtxoTxResponse) GetSignHashes() [][]byte {
	if x != nil {
		return x.SignHashes
	}
	return nil
}

type VerifySignedTxRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Symbol        string                 `protobuf:"bytes,3,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Network       string                 `protobuf:"bytes,4,opt,name=network,proto3" json:"network,omitempty"`
	// Deprecated: Marked as deprecated in dapplink/wallet.proto.
	Addresses     []string `protobuf:"bytes,5,rep,name=addresses,proto3" json:"addresses,omitempty"`
	SignedTxData  []byte   `protobuf:"bytes,6,opt,name=signed_tx_data,json=signedTxData,proto3" json:"signed_tx_data,omitempty"`
	Sender        string   `protobuf:"bytes,7,opt,name=sender,proto3" json:"sender,omitempty"`  // eth
	Height        int64    `protobuf:"varint,8,opt,name=height,proto3" json:"height,omitempty"` // optional, default to latest
	Vins          []*Vin   `protobuf:"bytes,9,rep,name=vins,proto3" json:"vins,omitempty"`      // btc
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifySignedTxRequest) Reset() {
	*x = VerifySignedTxRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifySignedTxRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifySignedTxRequest) ProtoMessage() {}

func (x *VerifySignedTxRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifySignedTxRequest.ProtoReflect.Descriptor instead.
func (*VerifySignedTxRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{45}
}

func (x *VerifySignedTxRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *VerifySignedTxRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *VerifySignedTxRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *VerifySignedTxRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

// Deprecated: Marked as deprecated in dapplink/wallet.proto.
func (x *VerifySignedTxRequest) GetAddresses() []string {
	if x != nil {
		return x.Addresses
	}
	return nil
}

func (x *VerifySignedTxRequest) GetSignedTxData() []byte {
	if x != nil {
		return x.SignedTxData
	}
	return nil
}

func (x *VerifySignedTxRequest) GetSender() string {
	if x != nil {
		return x.Sender
	}
	return ""
}

func (x *VerifySignedTxRequest) GetHeight() int64 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *VerifySignedTxRequest) GetVins() []*Vin {
	if x != nil {
		return x.Vins
	}
	return nil
}

type VerifySignedTxResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Verified      bool                   `protobuf:"varint,3,opt,name=verified,proto3" json:"verified,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifySignedTxResponse) Reset() {
	*x = VerifySignedTxResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifySignedTxResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifySignedTxResponse) ProtoMessage() {}

func (x *VerifySignedTxResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifySignedTxResponse.ProtoReflect.Descriptor instead.
func (*VerifySignedTxResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{46}
}

func (x *VerifySignedTxResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *VerifySignedTxResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *VerifySignedTxResponse) GetVerified() bool {
	if x != nil {
		return x.Verified
	}
	return false
}

type ABIBinToJSONRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Symbol        string                 `protobuf:"bytes,3,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Network       string                 `protobuf:"bytes,4,opt,name=network,proto3" json:"network,omitempty"`
	Code          string                 `protobuf:"bytes,5,opt,name=code,proto3" json:"code,omitempty"`
	Action        string                 `protobuf:"bytes,6,opt,name=action,proto3" json:"action,omitempty"`
	Bin           []byte                 `protobuf:"bytes,7,opt,name=bin,proto3" json:"bin,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ABIBinToJSONRequest) Reset() {
	*x = ABIBinToJSONRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ABIBinToJSONRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ABIBinToJSONRequest) ProtoMessage() {}

func (x *ABIBinToJSONRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ABIBinToJSONRequest.ProtoReflect.Descriptor instead.
func (*ABIBinToJSONRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{47}
}

func (x *ABIBinToJSONRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *ABIBinToJSONRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *ABIBinToJSONRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *ABIBinToJSONRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *ABIBinToJSONRequest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *ABIBinToJSONRequest) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *ABIBinToJSONRequest) GetBin() []byte {
	if x != nil {
		return x.Bin
	}
	return nil
}

type ABIBinToJSONResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	JsonString    string                 `protobuf:"bytes,3,opt,name=jsonString,proto3" json:"jsonString,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ABIBinToJSONResponse) Reset() {
	*x = ABIBinToJSONResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ABIBinToJSONResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ABIBinToJSONResponse) ProtoMessage() {}

func (x *ABIBinToJSONResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ABIBinToJSONResponse.ProtoReflect.Descriptor instead.
func (*ABIBinToJSONResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{48}
}

func (x *ABIBinToJSONResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *ABIBinToJSONResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ABIBinToJSONResponse) GetJsonString() string {
	if x != nil {
		return x.JsonString
	}
	return ""
}

type ABIJSONToBinRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Symbol        string                 `protobuf:"bytes,3,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Network       string                 `protobuf:"bytes,4,opt,name=network,proto3" json:"network,omitempty"`
	Code          string                 `protobuf:"bytes,5,opt,name=code,proto3" json:"code,omitempty"`
	Action        string                 `protobuf:"bytes,6,opt,name=action,proto3" json:"action,omitempty"`
	JsonString    string                 `protobuf:"bytes,7,opt,name=jsonString,proto3" json:"jsonString,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ABIJSONToBinRequest) Reset() {
	*x = ABIJSONToBinRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ABIJSONToBinRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ABIJSONToBinRequest) ProtoMessage() {}

func (x *ABIJSONToBinRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ABIJSONToBinRequest.ProtoReflect.Descriptor instead.
func (*ABIJSONToBinRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{49}
}

func (x *ABIJSONToBinRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *ABIJSONToBinRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *ABIJSONToBinRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *ABIJSONToBinRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *ABIJSONToBinRequest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *ABIJSONToBinRequest) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *ABIJSONToBinRequest) GetJsonString() string {
	if x != nil {
		return x.JsonString
	}
	return ""
}

type ABIJSONToBinResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Bin           []byte                 `protobuf:"bytes,3,opt,name=bin,proto3" json:"bin,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ABIJSONToBinResponse) Reset() {
	*x = ABIJSONToBinResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ABIJSONToBinResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ABIJSONToBinResponse) ProtoMessage() {}

func (x *ABIJSONToBinResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ABIJSONToBinResponse.ProtoReflect.Descriptor instead.
func (*ABIJSONToBinResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{50}
}

func (x *ABIJSONToBinResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *ABIJSONToBinResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ABIJSONToBinResponse) GetBin() []byte {
	if x != nil {
		return x.Bin
	}
	return nil
}

type BlockRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConsumerToken string                 `protobuf:"bytes,1,opt,name=consumer_token,json=consumerToken,proto3" json:"consumer_token,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockRequest) Reset() {
	*x = BlockRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockRequest) ProtoMessage() {}

func (x *BlockRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockRequest.ProtoReflect.Descriptor instead.
func (*BlockRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{51}
}

func (x *BlockRequest) GetConsumerToken() string {
	if x != nil {
		return x.ConsumerToken
	}
	return ""
}

func (x *BlockRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

type BlockResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Height        int64                  `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
	Hash          string                 `protobuf:"bytes,4,opt,name=hash,proto3" json:"hash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockResponse) Reset() {
	*x = BlockResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockResponse) ProtoMessage() {}

func (x *BlockResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockResponse.ProtoReflect.Descriptor instead.
func (*BlockResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{52}
}

func (x *BlockResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *BlockResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *BlockResponse) GetHeight() int64 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *BlockResponse) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

type BlockInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Chain         string                 `protobuf:"bytes,1,opt,name=chain,proto3" json:"chain,omitempty"`
	Height        string                 `protobuf:"bytes,2,opt,name=height,proto3" json:"height,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockInfoRequest) Reset() {
	*x = BlockInfoRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockInfoRequest) ProtoMessage() {}

func (x *BlockInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockInfoRequest.ProtoReflect.Descriptor instead.
func (*BlockInfoRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{53}
}

func (x *BlockInfoRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *BlockInfoRequest) GetHeight() string {
	if x != nil {
		return x.Height
	}
	return ""
}

type BlockInfoResponse struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Code          common.ReturnCode           `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                      `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Hash          string                      `protobuf:"bytes,3,opt,name=hash,proto3" json:"hash,omitempty"`
	BaseFee       string                      `protobuf:"bytes,4,opt,name=baseFee,proto3" json:"baseFee,omitempty"`
	Transactions  []*BlockInfoTransactionList `protobuf:"bytes,5,rep,name=transactions,proto3" json:"transactions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockInfoResponse) Reset() {
	*x = BlockInfoResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockInfoResponse) ProtoMessage() {}

func (x *BlockInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockInfoResponse.ProtoReflect.Descriptor instead.
func (*BlockInfoResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{54}
}

func (x *BlockInfoResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *BlockInfoResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *BlockInfoResponse) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *BlockInfoResponse) GetBaseFee() string {
	if x != nil {
		return x.BaseFee
	}
	return ""
}

func (x *BlockInfoResponse) GetTransactions() []*BlockInfoTransactionList {
	if x != nil {
		return x.Transactions
	}
	return nil
}

type BlockInfoTransactionList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	From          string                 `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"`
	To            string                 `protobuf:"bytes,2,opt,name=to,proto3" json:"to,omitempty"`
	Hash          string                 `protobuf:"bytes,3,opt,name=hash,proto3" json:"hash,omitempty"`
	Time          string                 `protobuf:"bytes,4,opt,name=time,proto3" json:"time,omitempty"`
	Amount        string                 `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockInfoTransactionList) Reset() {
	*x = BlockInfoTransactionList{}
	mi := &file_dapplink_wallet_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockInfoTransactionList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockInfoTransactionList) ProtoMessage() {}

func (x *BlockInfoTransactionList) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockInfoTransactionList.ProtoReflect.Descriptor instead.
func (*BlockInfoTransactionList) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{55}
}

func (x *BlockInfoTransactionList) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *BlockInfoTransactionList) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

func (x *BlockInfoTransactionList) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *BlockInfoTransactionList) GetTime() string {
	if x != nil {
		return x.Time
	}
	return ""
}

func (x *BlockInfoTransactionList) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

type BlockHeaderRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Height        string                 `protobuf:"bytes,1,opt,name=height,proto3" json:"height,omitempty"`
	Chain         string                 `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockHeaderRequest) Reset() {
	*x = BlockHeaderRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockHeaderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockHeaderRequest) ProtoMessage() {}

func (x *BlockHeaderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockHeaderRequest.ProtoReflect.Descriptor instead.
func (*BlockHeaderRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{56}
}

func (x *BlockHeaderRequest) GetHeight() string {
	if x != nil {
		return x.Height
	}
	return ""
}

func (x *BlockHeaderRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

type BlockHeaderResponse struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Code             common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg              string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	ParentHash       string                 `protobuf:"bytes,3,opt,name=ParentHash,proto3" json:"ParentHash,omitempty"`
	UncleHash        string                 `protobuf:"bytes,4,opt,name=UncleHash,proto3" json:"UncleHash,omitempty"`
	Coinbase         string                 `protobuf:"bytes,5,opt,name=Coinbase,proto3" json:"Coinbase,omitempty"`
	Root             string                 `protobuf:"bytes,6,opt,name=Root,proto3" json:"Root,omitempty"`
	TxHash           string                 `protobuf:"bytes,7,opt,name=TxHash,proto3" json:"TxHash,omitempty"`
	ReceiptHash      string                 `protobuf:"bytes,8,opt,name=ReceiptHash,proto3" json:"ReceiptHash,omitempty"`
	ParentBeaconRoot string                 `protobuf:"bytes,9,opt,name=ParentBeaconRoot,proto3" json:"ParentBeaconRoot,omitempty"`
	Difficulty       string                 `protobuf:"bytes,10,opt,name=Difficulty,proto3" json:"Difficulty,omitempty"`
	Number           string                 `protobuf:"bytes,11,opt,name=Number,proto3" json:"Number,omitempty"`
	GasLimit         uint64                 `protobuf:"varint,12,opt,name=GasLimit,proto3" json:"GasLimit,omitempty"`
	GasUsed          uint64                 `protobuf:"varint,13,opt,name=GasUsed,proto3" json:"GasUsed,omitempty"`
	Time             uint64                 `protobuf:"varint,14,opt,name=Time,proto3" json:"Time,omitempty"`
	Extra            string                 `protobuf:"bytes,15,opt,name=Extra,proto3" json:"Extra,omitempty"`
	MixDigest        string                 `protobuf:"bytes,16,opt,name=MixDigest,proto3" json:"MixDigest,omitempty"`
	Nonce            string                 `protobuf:"bytes,17,opt,name=Nonce,proto3" json:"Nonce,omitempty"`
	BaseFee          string                 `protobuf:"bytes,18,opt,name=BaseFee,proto3" json:"BaseFee,omitempty"`
	WithdrawalsHash  string                 `protobuf:"bytes,19,opt,name=WithdrawalsHash,proto3" json:"WithdrawalsHash,omitempty"`
	BlobGasUsed      uint64                 `protobuf:"varint,20,opt,name=BlobGasUsed,proto3" json:"BlobGasUsed,omitempty"`
	ExcessBlobGas    uint64                 `protobuf:"varint,21,opt,name=ExcessBlobGas,proto3" json:"ExcessBlobGas,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *BlockHeaderResponse) Reset() {
	*x = BlockHeaderResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockHeaderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockHeaderResponse) ProtoMessage() {}

func (x *BlockHeaderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockHeaderResponse.ProtoReflect.Descriptor instead.
func (*BlockHeaderResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{57}
}

func (x *BlockHeaderResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *BlockHeaderResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *BlockHeaderResponse) GetParentHash() string {
	if x != nil {
		return x.ParentHash
	}
	return ""
}

func (x *BlockHeaderResponse) GetUncleHash() string {
	if x != nil {
		return x.UncleHash
	}
	return ""
}

func (x *BlockHeaderResponse) GetCoinbase() string {
	if x != nil {
		return x.Coinbase
	}
	return ""
}

func (x *BlockHeaderResponse) GetRoot() string {
	if x != nil {
		return x.Root
	}
	return ""
}

func (x *BlockHeaderResponse) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

func (x *BlockHeaderResponse) GetReceiptHash() string {
	if x != nil {
		return x.ReceiptHash
	}
	return ""
}

func (x *BlockHeaderResponse) GetParentBeaconRoot() string {
	if x != nil {
		return x.ParentBeaconRoot
	}
	return ""
}

func (x *BlockHeaderResponse) GetDifficulty() string {
	if x != nil {
		return x.Difficulty
	}
	return ""
}

func (x *BlockHeaderResponse) GetNumber() string {
	if x != nil {
		return x.Number
	}
	return ""
}

func (x *BlockHeaderResponse) GetGasLimit() uint64 {
	if x != nil {
		return x.GasLimit
	}
	return 0
}

func (x *BlockHeaderResponse) GetGasUsed() uint64 {
	if x != nil {
		return x.GasUsed
	}
	return 0
}

func (x *BlockHeaderResponse) GetTime() uint64 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *BlockHeaderResponse) GetExtra() string {
	if x != nil {
		return x.Extra
	}
	return ""
}

func (x *BlockHeaderResponse) GetMixDigest() string {
	if x != nil {
		return x.MixDigest
	}
	return ""
}

func (x *BlockHeaderResponse) GetNonce() string {
	if x != nil {
		return x.Nonce
	}
	return ""
}

func (x *BlockHeaderResponse) GetBaseFee() string {
	if x != nil {
		return x.BaseFee
	}
	return ""
}

func (x *BlockHeaderResponse) GetWithdrawalsHash() string {
	if x != nil {
		return x.WithdrawalsHash
	}
	return ""
}

func (x *BlockHeaderResponse) GetBlobGasUsed() uint64 {
	if x != nil {
		return x.BlobGasUsed
	}
	return 0
}

func (x *BlockHeaderResponse) GetExcessBlobGas() uint64 {
	if x != nil {
		return x.ExcessBlobGas
	}
	return 0
}

type LatestSafeBlockHeaderRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Chain         string                 `protobuf:"bytes,1,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,2,opt,name=network,proto3" json:"network,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LatestSafeBlockHeaderRequest) Reset() {
	*x = LatestSafeBlockHeaderRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LatestSafeBlockHeaderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LatestSafeBlockHeaderRequest) ProtoMessage() {}

func (x *LatestSafeBlockHeaderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LatestSafeBlockHeaderRequest.ProtoReflect.Descriptor instead.
func (*LatestSafeBlockHeaderRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{58}
}

func (x *LatestSafeBlockHeaderRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *LatestSafeBlockHeaderRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

// 公共请求消息
type BasicRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Chain         string                 `protobuf:"bytes,1,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,2,opt,name=network,proto3" json:"network,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BasicRequest) Reset() {
	*x = BasicRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BasicRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BasicRequest) ProtoMessage() {}

func (x *BasicRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BasicRequest.ProtoReflect.Descriptor instead.
func (*BasicRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{59}
}

func (x *BasicRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *BasicRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

type BlockHeaderByHashRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Chain         string                 `protobuf:"bytes,1,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,2,opt,name=network,proto3" json:"network,omitempty"`
	Hash          string                 `protobuf:"bytes,3,opt,name=hash,proto3" json:"hash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockHeaderByHashRequest) Reset() {
	*x = BlockHeaderByHashRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockHeaderByHashRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockHeaderByHashRequest) ProtoMessage() {}

func (x *BlockHeaderByHashRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockHeaderByHashRequest.ProtoReflect.Descriptor instead.
func (*BlockHeaderByHashRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{60}
}

func (x *BlockHeaderByHashRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *BlockHeaderByHashRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *BlockHeaderByHashRequest) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

type BlockByRangeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Chain         string                 `protobuf:"bytes,1,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,2,opt,name=network,proto3" json:"network,omitempty"`
	Start         string                 `protobuf:"bytes,3,opt,name=start,proto3" json:"start,omitempty"`
	End           string                 `protobuf:"bytes,4,opt,name=end,proto3" json:"end,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockByRangeRequest) Reset() {
	*x = BlockByRangeRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockByRangeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockByRangeRequest) ProtoMessage() {}

func (x *BlockByRangeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockByRangeRequest.ProtoReflect.Descriptor instead.
func (*BlockByRangeRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{61}
}

func (x *BlockByRangeRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *BlockByRangeRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *BlockByRangeRequest) GetStart() string {
	if x != nil {
		return x.Start
	}
	return ""
}

func (x *BlockByRangeRequest) GetEnd() string {
	if x != nil {
		return x.End
	}
	return ""
}

type BlockData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Hash          string                 `protobuf:"bytes,1,opt,name=hash,proto3" json:"hash,omitempty"`
	BaseFee       string                 `protobuf:"bytes,2,opt,name=baseFee,proto3" json:"baseFee,omitempty"`
	Transactions  []*TxMessage           `protobuf:"bytes,3,rep,name=transactions,proto3" json:"transactions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockData) Reset() {
	*x = BlockData{}
	mi := &file_dapplink_wallet_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockData) ProtoMessage() {}

func (x *BlockData) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockData.ProtoReflect.Descriptor instead.
func (*BlockData) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{62}
}

func (x *BlockData) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *BlockData) GetBaseFee() string {
	if x != nil {
		return x.BaseFee
	}
	return ""
}

func (x *BlockData) GetTransactions() []*TxMessage {
	if x != nil {
		return x.Transactions
	}
	return nil
}

type BlockByRangeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Blocks        []*BlockData           `protobuf:"bytes,3,rep,name=Blocks,proto3" json:"Blocks,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockByRangeResponse) Reset() {
	*x = BlockByRangeResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[63]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockByRangeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockByRangeResponse) ProtoMessage() {}

func (x *BlockByRangeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[63]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockByRangeResponse.ProtoReflect.Descriptor instead.
func (*BlockByRangeResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{63}
}

func (x *BlockByRangeResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *BlockByRangeResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *BlockByRangeResponse) GetBlocks() []*BlockData {
	if x != nil {
		return x.Blocks
	}
	return nil
}

type TxReceiptByHashRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Chain         string                 `protobuf:"bytes,1,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,2,opt,name=network,proto3" json:"network,omitempty"`
	Hash          string                 `protobuf:"bytes,3,opt,name=hash,proto3" json:"hash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TxReceiptByHashRequest) Reset() {
	*x = TxReceiptByHashRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[64]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TxReceiptByHashRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxReceiptByHashRequest) ProtoMessage() {}

func (x *TxReceiptByHashRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[64]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxReceiptByHashRequest.ProtoReflect.Descriptor instead.
func (*TxReceiptByHashRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{64}
}

func (x *TxReceiptByHashRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *TxReceiptByHashRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *TxReceiptByHashRequest) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

type TxReceiptByHashResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Code              common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg               string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Type              uint32                 `protobuf:"varint,3,opt,name=Type,proto3" json:"Type,omitempty"`
	PostState         string                 `protobuf:"bytes,4,opt,name=PostState,proto3" json:"PostState,omitempty"`
	Status            uint64                 `protobuf:"varint,5,opt,name=Status,proto3" json:"Status,omitempty"`
	CumulativeGasUsed uint64                 `protobuf:"varint,6,opt,name=CumulativeGasUsed,proto3" json:"CumulativeGasUsed,omitempty"`
	GasUsed           uint64                 `protobuf:"varint,7,opt,name=GasUsed,proto3" json:"GasUsed,omitempty"`
	TxHash            string                 `protobuf:"bytes,8,opt,name=TxHash,proto3" json:"TxHash,omitempty"`
	ContractAddress   string                 `protobuf:"bytes,9,opt,name=ContractAddress,proto3" json:"ContractAddress,omitempty"`
	EffectiveGasPrice string                 `protobuf:"bytes,10,opt,name=EffectiveGasPrice,proto3" json:"EffectiveGasPrice,omitempty"`
	BlockHash         string                 `protobuf:"bytes,11,opt,name=BlockHash,proto3" json:"BlockHash,omitempty"`
	BlockNumber       string                 `protobuf:"bytes,12,opt,name=BlockNumber,proto3" json:"BlockNumber,omitempty"`
	TransactionIndex  uint64                 `protobuf:"varint,13,opt,name=TransactionIndex,proto3" json:"TransactionIndex,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *TxReceiptByHashResponse) Reset() {
	*x = TxReceiptByHashResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[65]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TxReceiptByHashResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxReceiptByHashResponse) ProtoMessage() {}

func (x *TxReceiptByHashResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[65]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxReceiptByHashResponse.ProtoReflect.Descriptor instead.
func (*TxReceiptByHashResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{65}
}

func (x *TxReceiptByHashResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *TxReceiptByHashResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *TxReceiptByHashResponse) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *TxReceiptByHashResponse) GetPostState() string {
	if x != nil {
		return x.PostState
	}
	return ""
}

func (x *TxReceiptByHashResponse) GetStatus() uint64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *TxReceiptByHashResponse) GetCumulativeGasUsed() uint64 {
	if x != nil {
		return x.CumulativeGasUsed
	}
	return 0
}

func (x *TxReceiptByHashResponse) GetGasUsed() uint64 {
	if x != nil {
		return x.GasUsed
	}
	return 0
}

func (x *TxReceiptByHashResponse) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

func (x *TxReceiptByHashResponse) GetContractAddress() string {
	if x != nil {
		return x.ContractAddress
	}
	return ""
}

func (x *TxReceiptByHashResponse) GetEffectiveGasPrice() string {
	if x != nil {
		return x.EffectiveGasPrice
	}
	return ""
}

func (x *TxReceiptByHashResponse) GetBlockHash() string {
	if x != nil {
		return x.BlockHash
	}
	return ""
}

func (x *TxReceiptByHashResponse) GetBlockNumber() string {
	if x != nil {
		return x.BlockNumber
	}
	return ""
}

func (x *TxReceiptByHashResponse) GetTransactionIndex() uint64 {
	if x != nil {
		return x.TransactionIndex
	}
	return 0
}

type StorageHashRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Chain         string                 `protobuf:"bytes,1,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,2,opt,name=network,proto3" json:"network,omitempty"`
	Address       string                 `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
	BlockNumber   string                 `protobuf:"bytes,4,opt,name=blockNumber,proto3" json:"blockNumber,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StorageHashRequest) Reset() {
	*x = StorageHashRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[66]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StorageHashRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StorageHashRequest) ProtoMessage() {}

func (x *StorageHashRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[66]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StorageHashRequest.ProtoReflect.Descriptor instead.
func (*StorageHashRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{66}
}

func (x *StorageHashRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *StorageHashRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *StorageHashRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *StorageHashRequest) GetBlockNumber() string {
	if x != nil {
		return x.BlockNumber
	}
	return ""
}

type StorageHashResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Hash          string                 `protobuf:"bytes,3,opt,name=hash,proto3" json:"hash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StorageHashResponse) Reset() {
	*x = StorageHashResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[67]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StorageHashResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StorageHashResponse) ProtoMessage() {}

func (x *StorageHashResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[67]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StorageHashResponse.ProtoReflect.Descriptor instead.
func (*StorageHashResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{67}
}

func (x *StorageHashResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *StorageHashResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *StorageHashResponse) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

type FilterLogsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Chain         string                 `protobuf:"bytes,1,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,2,opt,name=network,proto3" json:"network,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterLogsRequest) Reset() {
	*x = FilterLogsRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[68]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterLogsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterLogsRequest) ProtoMessage() {}

func (x *FilterLogsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[68]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterLogsRequest.ProtoReflect.Descriptor instead.
func (*FilterLogsRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{68}
}

func (x *FilterLogsRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *FilterLogsRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

type Log struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Address       string                 `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	Topics        []string               `protobuf:"bytes,2,rep,name=topics,proto3" json:"topics,omitempty"`
	Data          string                 `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	BlockNumber   string                 `protobuf:"bytes,4,opt,name=blockNumber,proto3" json:"blockNumber,omitempty"`
	TxHash        string                 `protobuf:"bytes,5,opt,name=txHash,proto3" json:"txHash,omitempty"`
	TxIndex       uint64                 `protobuf:"varint,6,opt,name=txIndex,proto3" json:"txIndex,omitempty"`
	BlockHash     string                 `protobuf:"bytes,7,opt,name=blockHash,proto3" json:"blockHash,omitempty"`
	Index         uint64                 `protobuf:"varint,8,opt,name=index,proto3" json:"index,omitempty"`
	Removed       bool                   `protobuf:"varint,9,opt,name=removed,proto3" json:"removed,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Log) Reset() {
	*x = Log{}
	mi := &file_dapplink_wallet_proto_msgTypes[69]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Log) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Log) ProtoMessage() {}

func (x *Log) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[69]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Log.ProtoReflect.Descriptor instead.
func (*Log) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{69}
}

func (x *Log) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *Log) GetTopics() []string {
	if x != nil {
		return x.Topics
	}
	return nil
}

func (x *Log) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *Log) GetBlockNumber() string {
	if x != nil {
		return x.BlockNumber
	}
	return ""
}

func (x *Log) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

func (x *Log) GetTxIndex() uint64 {
	if x != nil {
		return x.TxIndex
	}
	return 0
}

func (x *Log) GetBlockHash() string {
	if x != nil {
		return x.BlockHash
	}
	return ""
}

func (x *Log) GetIndex() uint64 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *Log) GetRemoved() bool {
	if x != nil {
		return x.Removed
	}
	return false
}

type FilterLogsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	ToBlockHeader *BlockHeaderResponse   `protobuf:"bytes,3,opt,name=toBlockHeader,proto3" json:"toBlockHeader,omitempty"`
	Logs          []*Log                 `protobuf:"bytes,4,rep,name=logs,proto3" json:"logs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterLogsResponse) Reset() {
	*x = FilterLogsResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[70]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterLogsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterLogsResponse) ProtoMessage() {}

func (x *FilterLogsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[70]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterLogsResponse.ProtoReflect.Descriptor instead.
func (*FilterLogsResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{70}
}

func (x *FilterLogsResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *FilterLogsResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *FilterLogsResponse) GetToBlockHeader() *BlockHeaderResponse {
	if x != nil {
		return x.ToBlockHeader
	}
	return nil
}

func (x *FilterLogsResponse) GetLogs() []*Log {
	if x != nil {
		return x.Logs
	}
	return nil
}

type TxCountByAddressRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Chain         string                 `protobuf:"bytes,1,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,2,opt,name=network,proto3" json:"network,omitempty"`
	Address       string                 `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TxCountByAddressRequest) Reset() {
	*x = TxCountByAddressRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[71]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TxCountByAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxCountByAddressRequest) ProtoMessage() {}

func (x *TxCountByAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[71]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxCountByAddressRequest.ProtoReflect.Descriptor instead.
func (*TxCountByAddressRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{71}
}

func (x *TxCountByAddressRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *TxCountByAddressRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *TxCountByAddressRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type TxCountByAddressResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Count         uint64                 `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TxCountByAddressResponse) Reset() {
	*x = TxCountByAddressResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[72]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TxCountByAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxCountByAddressResponse) ProtoMessage() {}

func (x *TxCountByAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[72]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxCountByAddressResponse.ProtoReflect.Descriptor instead.
func (*TxCountByAddressResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{72}
}

func (x *TxCountByAddressResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *TxCountByAddressResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *TxCountByAddressResponse) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type SuggestGasPriceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Chain         string                 `protobuf:"bytes,1,opt,name=chain,proto3" json:"chain,omitempty"`
	Network       string                 `protobuf:"bytes,2,opt,name=network,proto3" json:"network,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SuggestGasPriceRequest) Reset() {
	*x = SuggestGasPriceRequest{}
	mi := &file_dapplink_wallet_proto_msgTypes[73]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SuggestGasPriceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuggestGasPriceRequest) ProtoMessage() {}

func (x *SuggestGasPriceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[73]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuggestGasPriceRequest.ProtoReflect.Descriptor instead.
func (*SuggestGasPriceRequest) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{73}
}

func (x *SuggestGasPriceRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *SuggestGasPriceRequest) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

type SuggestGasPriceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          common.ReturnCode      `protobuf:"varint,1,opt,name=code,proto3,enum=dapplink.ReturnCode" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	GasPrice      string                 `protobuf:"bytes,3,opt,name=gasPrice,proto3" json:"gasPrice,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SuggestGasPriceResponse) Reset() {
	*x = SuggestGasPriceResponse{}
	mi := &file_dapplink_wallet_proto_msgTypes[74]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SuggestGasPriceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuggestGasPriceResponse) ProtoMessage() {}

func (x *SuggestGasPriceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dapplink_wallet_proto_msgTypes[74]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuggestGasPriceResponse.ProtoReflect.Descriptor instead.
func (*SuggestGasPriceResponse) Descriptor() ([]byte, []int) {
	return file_dapplink_wallet_proto_rawDescGZIP(), []int{74}
}

func (x *SuggestGasPriceResponse) GetCode() common.ReturnCode {
	if x != nil {
		return x.Code
	}
	return common.ReturnCode(0)
}

func (x *SuggestGasPriceResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SuggestGasPriceResponse) GetGasPrice() string {
	if x != nil {
		return x.GasPrice
	}
	return ""
}

var File_dapplink_wallet_proto protoreflect.FileDescriptor

var file_dapplink_wallet_proto_rawDesc = string([]byte{
	0x0a, 0x15, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x1a, 0x15, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x23, 0x0a, 0x07, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x22, 0x1d, 0x0a, 0x05, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x22, 0xf9, 0x02, 0x0a, 0x09, 0x54, 0x78, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x68, 0x61, 0x73, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x2e, 0x0a, 0x05, 0x66,
	0x72, 0x6f, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x64, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x52, 0x05, 0x66, 0x72, 0x6f, 0x6d, 0x73, 0x12, 0x2a, 0x0a, 0x03, 0x74,
	0x6f, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x52, 0x03, 0x74, 0x6f, 0x73, 0x12, 0x2e, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x66, 0x65, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x66, 0x65, 0x65, 0x12, 0x31, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x64, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x54, 0x78, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x22,
	0x61, 0x0a, 0x03, 0x56, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e,
	0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x22, 0x4e, 0x0a, 0x04, 0x56, 0x6f, 0x75, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x22, 0x6c, 0x0a, 0x13, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x69,
	0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x22, 0x6c, 0x0a, 0x14, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x69, 0x6e, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x22, 0x8d,
	0x01, 0x0a, 0x15, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12,
	0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12,
	0x1d, 0x0a, 0x0a, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b, 0x65, 0x79, 0x22, 0x6e,
	0x0a, 0x16, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x9e,
	0x01, 0x0a, 0x13, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a,
	0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68,
	0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22,
	0xbc, 0x01, 0x0a, 0x14, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x61,
	0x6e, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0d, 0x63, 0x61, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61,
	0x6c, 0x12, 0x2b, 0x0a, 0x11, 0x63, 0x61, 0x6e, 0x6f, 0x6e, 0x69, 0x63, 0x61, 0x6c, 0x5f, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x61,
	0x6e, 0x6f, 0x6e, 0x69, 0x63, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x93,
	0x01, 0x0a, 0x0c, 0x4e, 0x6f, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x69, 0x6e,
	0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x22, 0x61, 0x0a, 0x0d, 0x4e, 0x6f, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52,
	0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x22, 0xac, 0x01, 0x0a, 0x0f, 0x47, 0x61, 0x73, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x69, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x61, 0x77, 0x54, 0x78, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x61, 0x77, 0x54, 0x78, 0x12, 0x18, 0x0a, 0x07,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x60, 0x0a, 0x10, 0x47, 0x61, 0x73, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x67, 0x61, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x67, 0x61, 0x73, 0x22, 0xec, 0x01, 0x0a, 0x0e, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x69, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x29, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x2a, 0x0a, 0x10, 0x70,
	0x72, 0x6f, 0x70, 0x6f, 0x73, 0x65, 0x72, 0x4b, 0x65, 0x79, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x70, 0x72, 0x6f, 0x70, 0x6f, 0x73, 0x65, 0x72, 0x4b,
	0x65, 0x79, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x8f, 0x01, 0x0a, 0x0f, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x73, 0x65, 0x71, 0x75, 0x65,
	0x6e, 0x63, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x95, 0x01, 0x0a, 0x0e, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e,
	0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x69,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x69, 0x6e, 0x12, 0x18, 0x0a,
	0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x22, 0xaa, 0x01, 0x0a, 0x0f, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52,
	0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x71, 0x75,
	0x65, 0x6e, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x71, 0x75,
	0x65, 0x6e, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x22, 0x95,
	0x01, 0x0a, 0x0e, 0x4d, 0x69, 0x6e, 0x52, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f,
	0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x18, 0x0a, 0x07,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x63, 0x0a, 0x0f, 0x4d, 0x69, 0x6e, 0x52, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x91, 0x01, 0x0a, 0x0d,
	0x53, 0x65, 0x6e, 0x64, 0x54, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a,
	0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x69, 0x6e, 0x12, 0x18,
	0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x61, 0x77, 0x5f,
	0x74, 0x78, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x61, 0x77, 0x54, 0x78, 0x22,
	0x65, 0x0a, 0x0e, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x78, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72,
	0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x17, 0x0a,
	0x07, 0x74, 0x78, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x74, 0x78, 0x48, 0x61, 0x73, 0x68, 0x22, 0x8a, 0x02, 0x0a, 0x10, 0x54, 0x78, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x69, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x29, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x63,
	0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x75, 0x72,
	0x73, 0x6f, 0x72, 0x22, 0x7b, 0x0a, 0x11, 0x54, 0x78, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x12, 0x2a, 0x0a, 0x02, 0x74, 0x78, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x2e, 0x54, 0x78, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x02, 0x74, 0x78,
	0x22, 0x8e, 0x01, 0x0a, 0x0d, 0x54, 0x78, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63,
	0x6f, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x12, 0x0a,
	0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73,
	0x68, 0x22, 0x78, 0x0a, 0x0e, 0x54, 0x78, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74,
	0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12,
	0x2a, 0x0a, 0x02, 0x74, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x64, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x54, 0x78,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x02, 0x74, 0x78, 0x22, 0xa4, 0x01, 0x0a, 0x0b,
	0x55, 0x74, 0x78, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62,
	0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c,
	0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x26, 0x0a, 0x03, 0x76, 0x69,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x56, 0x69, 0x6e, 0x52, 0x03, 0x76,
	0x69, 0x6e, 0x22, 0x64, 0x0a, 0x0c, 0x55, 0x74, 0x78, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75,
	0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x18,
	0x0a, 0x07, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x6e, 0x74, 0x22, 0xe4, 0x01, 0x0a, 0x0d, 0x55, 0x6e, 0x73,
	0x70, 0x65, 0x6e, 0x74, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x2b, 0x0a, 0x12, 0x74, 0x78,
	0x5f, 0x68, 0x61, 0x73, 0x68, 0x5f, 0x62, 0x69, 0x67, 0x5f, 0x65, 0x6e, 0x64, 0x69, 0x61, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x74, 0x78, 0x48, 0x61, 0x73, 0x68, 0x42, 0x69,
	0x67, 0x45, 0x6e, 0x64, 0x69, 0x61, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x78, 0x5f, 0x68, 0x61,
	0x73, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x78, 0x48, 0x61, 0x73, 0x68,
	0x12, 0x1e, 0x0a, 0x0b, 0x74, 0x78, 0x5f, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x74, 0x78, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x4e,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x24,
	0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x78, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x74, 0x78, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22,
	0xea, 0x01, 0x0a, 0x15, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x6e, 0x74, 0x4f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x18,
	0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6f, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22, 0x9d, 0x01, 0x0a,
	0x16, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x6e, 0x74, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x12, 0x47, 0x0a, 0x0f, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x6e, 0x74, 0x5f, 0x6f,
	0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x64,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x55,
	0x6e, 0x73, 0x70, 0x65, 0x6e, 0x74, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x0e, 0x75, 0x6e,
	0x73, 0x70, 0x65, 0x6e, 0x74, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x22, 0x9b, 0x01, 0x0a,
	0x16, 0x55, 0x74, 0x78, 0x6f, 0x49, 0x6e, 0x73, 0x46, 0x72, 0x6f, 0x6d, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14,
	0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x18, 0x0a, 0x07,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x77, 0x0a, 0x0f, 0x55, 0x74,
	0x78, 0x6f, 0x49, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64,
	0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x28, 0x0a, 0x04, 0x76, 0x69, 0x6e,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x56, 0x69, 0x6e, 0x52, 0x04, 0x76,
	0x69, 0x6e, 0x73, 0x22, 0xdf, 0x01, 0x0a, 0x11, 0x54, 0x78, 0x46, 0x72, 0x6f, 0x6d, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x18,
	0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x61, 0x77, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x72, 0x61, 0x77, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x28, 0x0a, 0x04, 0x76,
	0x69, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x56, 0x69, 0x6e, 0x52,
	0x04, 0x76, 0x69, 0x6e, 0x73, 0x22, 0xe0, 0x03, 0x0a, 0x11, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x54, 0x78, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x78, 0x5f, 0x68, 0x61,
	0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x78, 0x48, 0x61, 0x73, 0x68,
	0x12, 0x31, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x19, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x2e, 0x54, 0x78, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x74, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x6d, 0x65, 0x6d, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d,
	0x65, 0x6d, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x05, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x73,
	0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61,
	0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x73, 0x5f, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x73, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6f, 0x73, 0x74, 0x5f, 0x66, 0x65, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x73, 0x74, 0x46, 0x65, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x08, 0x73, 0x69, 0x67, 0x6e, 0x48, 0x61, 0x73, 0x68, 0x12, 0x29, 0x0a,
	0x10, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0xed, 0x02, 0x0a, 0x0e, 0x55, 0x74, 0x78,
	0x6f, 0x54, 0x78, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x78, 0x5f, 0x68, 0x61,
	0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x78, 0x48, 0x61, 0x73, 0x68,
	0x12, 0x31, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x19, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x2e, 0x54, 0x78, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x28, 0x0a, 0x04, 0x76, 0x69, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x2e, 0x56, 0x69, 0x6e, 0x52, 0x04, 0x76, 0x69, 0x6e, 0x73, 0x12, 0x2b, 0x0a,
	0x05, 0x76, 0x6f, 0x75, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x64,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x56,
	0x6f, 0x75, 0x74, 0x52, 0x05, 0x76, 0x6f, 0x75, 0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x69,
	0x67, 0x6e, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0c, 0x52,
	0x0a, 0x73, 0x69, 0x67, 0x6e, 0x48, 0x61, 0x73, 0x68, 0x65, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x63,
	0x6f, 0x73, 0x74, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63,
	0x6f, 0x73, 0x74, 0x46, 0x65, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f,
	0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xf0, 0x01, 0x0a, 0x17, 0x54, 0x78, 0x46,
	0x72, 0x6f, 0x6d, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x74, 0x78,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x73, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x54, 0x78, 0x44, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x12, 0x28, 0x0a, 0x04, 0x76, 0x69, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x2e, 0x56, 0x69, 0x6e, 0x52, 0x04, 0x76, 0x69, 0x6e, 0x73, 0x22, 0xe3, 0x01, 0x0a, 0x1c,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x54, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e,
	0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d,
	0x62, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f,
	0x6c, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x17, 0x0a, 0x07, 0x74,
	0x78, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x74, 0x78,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x6b, 0x65, 0x79,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b, 0x65,
	0x79, 0x22, 0x8e, 0x01, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x69, 0x67, 0x6e,
	0x65, 0x64, 0x54, 0x78, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x69, 0x67, 0x6e,
	0x65, 0x64, 0x5f, 0x74, 0x78, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x0c, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x78, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12,
	0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x68, 0x61,
	0x73, 0x68, 0x22, 0xd2, 0x02, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x54, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a,
	0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79,
	0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62,
	0x6f, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x12, 0x0a, 0x04,
	0x66, 0x72, 0x6f, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d,
	0x12, 0x0e, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x74, 0x6f,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x65, 0x6d, 0x6f,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x65, 0x6d, 0x6f, 0x12, 0x1b, 0x0a, 0x09,
	0x67, 0x61, 0x73, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x67, 0x61, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x73,
	0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61,
	0x73, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x12, 0x29, 0x0a, 0x10,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x8b, 0x01, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x78, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74,
	0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12,
	0x17, 0x0a, 0x07, 0x74, 0x78, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x06, 0x74, 0x78, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e,
	0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x73, 0x69, 0x67,
	0x6e, 0x48, 0x61, 0x73, 0x68, 0x22, 0xe4, 0x01, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x55, 0x74, 0x78, 0x6f, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x78, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68,
	0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x78, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x06, 0x74, 0x78, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x0a, 0x0a, 0x73,
	0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0c, 0x52,
	0x0a, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x70,
	0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0c,
	0x52, 0x0a, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b, 0x65, 0x79, 0x73, 0x22, 0xed, 0x01, 0x0a,
	0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x74, 0x78, 0x6f, 0x54, 0x78, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x12, 0x28, 0x0a, 0x04, 0x76, 0x69, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x2e, 0x56, 0x69, 0x6e, 0x52, 0x04, 0x76, 0x69, 0x6e, 0x73, 0x12, 0x2b, 0x0a,
	0x05, 0x76, 0x6f, 0x75, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x64,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x56,
	0x6f, 0x75, 0x74, 0x52, 0x05, 0x76, 0x6f, 0x75, 0x74, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x66, 0x65,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x66, 0x65, 0x65, 0x22, 0x8c, 0x01, 0x0a,
	0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x74, 0x78, 0x6f, 0x54, 0x78, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52,
	0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x78, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x06, 0x74, 0x78, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x69,
	0x67, 0x6e, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0c, 0x52,
	0x0a, 0x73, 0x69, 0x67, 0x6e, 0x48, 0x61, 0x73, 0x68, 0x65, 0x73, 0x22, 0xa8, 0x02, 0x0a, 0x15,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x78, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x12, 0x20, 0x0a, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x09, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64,
	0x5f, 0x74, 0x78, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c,
	0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x78, 0x44, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x28, 0x0a, 0x04,
	0x76, 0x69, 0x6e, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x56, 0x69, 0x6e,
	0x52, 0x04, 0x76, 0x69, 0x6e, 0x73, 0x22, 0x70, 0x0a, 0x16, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79,
	0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x78, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14,
	0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e,
	0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x1a, 0x0a, 0x08,
	0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x22, 0xc2, 0x01, 0x0a, 0x13, 0x41, 0x42, 0x49,
	0x42, 0x69, 0x6e, 0x54, 0x6f, 0x4a, 0x53, 0x4f, 0x4e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x62,
	0x69, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03, 0x62, 0x69, 0x6e, 0x22, 0x72, 0x0a,
	0x14, 0x41, 0x42, 0x49, 0x42, 0x69, 0x6e, 0x54, 0x6f, 0x4a, 0x53, 0x4f, 0x4e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52,
	0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x6a, 0x73, 0x6f, 0x6e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6a, 0x73, 0x6f, 0x6e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x22, 0xd0, 0x01, 0x0a, 0x13, 0x41, 0x42, 0x49, 0x4a, 0x53, 0x4f, 0x4e, 0x54, 0x6f, 0x42,
	0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x18,
	0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x6a, 0x73, 0x6f, 0x6e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6a, 0x73, 0x6f, 0x6e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x22, 0x64, 0x0a, 0x14, 0x41, 0x42, 0x49, 0x4a, 0x53, 0x4f, 0x4e, 0x54,
	0x6f, 0x42, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x62, 0x69, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03, 0x62, 0x69, 0x6e, 0x22, 0x4b, 0x0a, 0x0c, 0x42, 0x6c,
	0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x22, 0x77, 0x0a, 0x0d, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x68, 0x61, 0x73, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68,
	0x22, 0x40, 0x0a, 0x10, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x22, 0xcc, 0x01, 0x0a, 0x11, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x73, 0x65,
	0x46, 0x65, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x61, 0x73, 0x65, 0x46,
	0x65, 0x65, 0x12, 0x4d, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x49, 0x6e, 0x66, 0x6f, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x22, 0x7e, 0x0a, 0x18, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x72, 0x6f,
	0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x74,
	0x6f, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x68, 0x61, 0x73, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0x42, 0x0a, 0x12, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x22, 0xfd, 0x04, 0x0a, 0x13, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64,
	0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x48, 0x61, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x50,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x48, 0x61, 0x73, 0x68, 0x12, 0x1c, 0x0a, 0x09, 0x55, 0x6e, 0x63,
	0x6c, 0x65, 0x48, 0x61, 0x73, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x55, 0x6e,
	0x63, 0x6c, 0x65, 0x48, 0x61, 0x73, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x43, 0x6f, 0x69, 0x6e, 0x62,
	0x61, 0x73, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x43, 0x6f, 0x69, 0x6e, 0x62,
	0x61, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x52, 0x6f, 0x6f, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x52, 0x6f, 0x6f, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x78, 0x48, 0x61, 0x73,
	0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x54, 0x78, 0x48, 0x61, 0x73, 0x68, 0x12,
	0x20, 0x0a, 0x0b, 0x52, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x48, 0x61, 0x73, 0x68, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x52, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x48, 0x61, 0x73,
	0x68, 0x12, 0x2a, 0x0a, 0x10, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x42, 0x65, 0x61, 0x63, 0x6f,
	0x6e, 0x52, 0x6f, 0x6f, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x50, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x42, 0x65, 0x61, 0x63, 0x6f, 0x6e, 0x52, 0x6f, 0x6f, 0x74, 0x12, 0x1e, 0x0a,
	0x0a, 0x44, 0x69, 0x66, 0x66, 0x69, 0x63, 0x75, 0x6c, 0x74, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x44, 0x69, 0x66, 0x66, 0x69, 0x63, 0x75, 0x6c, 0x74, 0x79, 0x12, 0x16, 0x0a,
	0x06, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x47, 0x61, 0x73, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x47, 0x61, 0x73, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x12, 0x18, 0x0a, 0x07, 0x47, 0x61, 0x73, 0x55, 0x73, 0x65, 0x64, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x07, 0x47, 0x61, 0x73, 0x55, 0x73, 0x65, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x45, 0x78, 0x74, 0x72, 0x61, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x1c, 0x0a, 0x09, 0x4d, 0x69, 0x78, 0x44, 0x69, 0x67, 0x65,
	0x73, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4d, 0x69, 0x78, 0x44, 0x69, 0x67,
	0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x4e, 0x6f, 0x6e, 0x63, 0x65, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x4e, 0x6f, 0x6e, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x42, 0x61, 0x73,
	0x65, 0x46, 0x65, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x42, 0x61, 0x73, 0x65,
	0x46, 0x65, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61,
	0x6c, 0x73, 0x48, 0x61, 0x73, 0x68, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x57, 0x69,
	0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x6c, 0x73, 0x48, 0x61, 0x73, 0x68, 0x12, 0x20, 0x0a,
	0x0b, 0x42, 0x6c, 0x6f, 0x62, 0x47, 0x61, 0x73, 0x55, 0x73, 0x65, 0x64, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0b, 0x42, 0x6c, 0x6f, 0x62, 0x47, 0x61, 0x73, 0x55, 0x73, 0x65, 0x64, 0x12,
	0x24, 0x0a, 0x0d, 0x45, 0x78, 0x63, 0x65, 0x73, 0x73, 0x42, 0x6c, 0x6f, 0x62, 0x47, 0x61, 0x73,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x45, 0x78, 0x63, 0x65, 0x73, 0x73, 0x42, 0x6c,
	0x6f, 0x62, 0x47, 0x61, 0x73, 0x22, 0x4e, 0x0a, 0x1c, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x53,
	0x61, 0x66, 0x65, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x22, 0x3e, 0x0a, 0x0c, 0x42, 0x61, 0x73, 0x69, 0x63, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x22, 0x5e, 0x0a, 0x18, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x42, 0x79, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x68, 0x61, 0x73, 0x68, 0x22, 0x6d, 0x0a, 0x13, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x42, 0x79,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x14, 0x0a, 0x05,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x65, 0x6e, 0x64, 0x22, 0x79, 0x0a, 0x09, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x68, 0x61, 0x73, 0x68, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x73, 0x65, 0x46, 0x65, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x61, 0x73, 0x65, 0x46, 0x65, 0x65, 0x12,
	0x3e, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x54, 0x78, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22,
	0x86, 0x01, 0x0a, 0x14, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x42, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x12, 0x32, 0x0a, 0x06, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x06, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x22, 0x5c, 0x0a, 0x16, 0x54, 0x78, 0x52, 0x65,
	0x63, 0x65, 0x69, 0x70, 0x74, 0x42, 0x79, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x22, 0xc3, 0x03, 0x0a, 0x17, 0x54, 0x78, 0x52, 0x65, 0x63,
	0x65, 0x69, 0x70, 0x74, 0x42, 0x79, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75,
	0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x12,
	0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x50, 0x6f, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x50, 0x6f, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2c, 0x0a, 0x11, 0x43, 0x75, 0x6d, 0x75,
	0x6c, 0x61, 0x74, 0x69, 0x76, 0x65, 0x47, 0x61, 0x73, 0x55, 0x73, 0x65, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x11, 0x43, 0x75, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x76, 0x65, 0x47,
	0x61, 0x73, 0x55, 0x73, 0x65, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x47, 0x61, 0x73, 0x55, 0x73, 0x65,
	0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x47, 0x61, 0x73, 0x55, 0x73, 0x65, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x54, 0x78, 0x48, 0x61, 0x73, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x54, 0x78, 0x48, 0x61, 0x73, 0x68, 0x12, 0x28, 0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x2c, 0x0a, 0x11, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47,
	0x61, 0x73, 0x50, 0x72, 0x69, 0x63, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x45,
	0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47, 0x61, 0x73, 0x50, 0x72, 0x69, 0x63, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x61, 0x73, 0x68, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x61, 0x73, 0x68, 0x12, 0x20,
	0x0a, 0x0b, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x2a, 0x0a, 0x10, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x64, 0x65, 0x78, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x80, 0x01, 0x0a,
	0x12, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x20, 0x0a,
	0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22,
	0x65, 0x0a, 0x13, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d,
	0x73, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x22, 0x43, 0x0a, 0x11, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x22, 0xed, 0x01, 0x0a, 0x03,
	0x4c, 0x6f, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x16, 0x0a,
	0x06, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x74,
	0x6f, 0x70, 0x69, 0x63, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x20, 0x0a, 0x0b, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x74,
	0x78, 0x48, 0x61, 0x73, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x78, 0x48,
	0x61, 0x73, 0x68, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x78, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x74, 0x78, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1c, 0x0a,
	0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x61, 0x73, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x61, 0x73, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x64, 0x22, 0xc6, 0x01, 0x0a, 0x12,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75,
	0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x4a,
	0x0a, 0x0d, 0x74, 0x6f, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0d, 0x74, 0x6f, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x04, 0x6c, 0x6f,
	0x67, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x4c, 0x6f, 0x67, 0x52, 0x04,
	0x6c, 0x6f, 0x67, 0x73, 0x22, 0x63, 0x0a, 0x17, 0x54, 0x78, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42,
	0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12,
	0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x6c, 0x0a, 0x18, 0x54, 0x78, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52,
	0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x48, 0x0a, 0x16, 0x53, 0x75, 0x67, 0x67, 0x65,
	0x73, 0x74, 0x47, 0x61, 0x73, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x22, 0x71, 0x0a, 0x17, 0x53, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x47, 0x61, 0x73, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x67, 0x61, 0x73, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x73, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x2a, 0x64, 0x0a, 0x08, 0x54, 0x78, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x0c, 0x0a, 0x08, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x00, 0x12, 0x0b,
	0x0a, 0x07, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x46,
	0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x10, 0x03, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0x04, 0x12,
	0x09, 0x0a, 0x05, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x10, 0x05, 0x32, 0x92, 0x1d, 0x0a, 0x0d, 0x57,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x60, 0x0a, 0x0f,
	0x67, 0x65, 0x74, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x12,
	0x24, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x2e, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x43,
	0x6f, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x63,
	0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x26, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x5d, 0x0a, 0x0c, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x24, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x64, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x4b, 0x0a, 0x08, 0x67, 0x65, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x1d,
	0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e,
	0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x65, 0x0a, 0x16, 0x67, 0x65, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x42, 0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x23, 0x2e, 0x64, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24,
	0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4b, 0x0a, 0x08, 0x67, 0x65, 0x74, 0x4e, 0x6f, 0x6e,
	0x63, 0x65, 0x12, 0x1d, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x4e, 0x6f, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1e, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x2e, 0x4e, 0x6f, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x54, 0x0a, 0x0b, 0x67, 0x65, 0x74, 0x47, 0x61, 0x73, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x12, 0x20, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x47, 0x61, 0x73, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x47, 0x61, 0x73, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x51, 0x0a, 0x0a, 0x67, 0x65, 0x74,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x1f, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x51, 0x0a, 0x0a,
	0x67, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x2e, 0x64, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x64, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x51, 0x0a, 0x0a, 0x67, 0x65, 0x74, 0x4d, 0x69, 0x6e, 0x52, 0x65, 0x6e, 0x74, 0x12, 0x1f, 0x2e,
	0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e,
	0x4d, 0x69, 0x6e, 0x52, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20,
	0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x2e, 0x4d, 0x69, 0x6e, 0x52, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x4b, 0x0a, 0x06, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x78, 0x12, 0x1e, 0x2e, 0x64,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x53,
	0x65, 0x6e, 0x64, 0x54, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x64,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x53,
	0x65, 0x6e, 0x64, 0x54, 0x78, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x59, 0x0a, 0x0e, 0x67, 0x65, 0x74, 0x54, 0x78, 0x42, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x12, 0x21, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x2e, 0x54, 0x78, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x54, 0x78, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x50, 0x0a, 0x0b, 0x67, 0x65,
	0x74, 0x54, 0x78, 0x42, 0x79, 0x48, 0x61, 0x73, 0x68, 0x12, 0x1e, 0x2e, 0x64, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x54, 0x78, 0x48, 0x61,
	0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x64, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x54, 0x78, 0x48, 0x61,
	0x73, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x48, 0x0a, 0x07,
	0x67, 0x65, 0x74, 0x55, 0x74, 0x78, 0x6f, 0x12, 0x1c, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x55, 0x74, 0x78, 0x6f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x55, 0x74, 0x78, 0x6f, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x66, 0x0a, 0x11, 0x67, 0x65, 0x74, 0x55, 0x6e, 0x73,
	0x70, 0x65, 0x6e, 0x74, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x12, 0x26, 0x2e, 0x64, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x55, 0x6e,
	0x73, 0x70, 0x65, 0x6e, 0x74, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x6e, 0x74, 0x4f, 0x75, 0x74,
	0x70, 0x75, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5f,
	0x0a, 0x12, 0x67, 0x65, 0x74, 0x55, 0x74, 0x78, 0x6f, 0x49, 0x6e, 0x73, 0x46, 0x72, 0x6f, 0x6d,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x27, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x55, 0x74, 0x78, 0x6f, 0x49, 0x6e, 0x73, 0x46, 0x72,
	0x6f, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e,
	0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e,
	0x55, 0x74, 0x78, 0x6f, 0x49, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x5e, 0x0a, 0x14, 0x67, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x78, 0x46,
	0x72, 0x6f, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x12, 0x22, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x54, 0x78, 0x46, 0x72, 0x6f, 0x6d,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x64, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x78, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x58, 0x0a, 0x11, 0x67, 0x65, 0x74, 0x55, 0x74, 0x78, 0x6f, 0x54, 0x78, 0x46, 0x72, 0x6f, 0x6d,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x22, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x54, 0x78, 0x46, 0x72, 0x6f, 0x6d, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x55, 0x74, 0x78, 0x6f, 0x54,
	0x78, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6a, 0x0a, 0x1a, 0x67, 0x65, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x78, 0x46, 0x72, 0x6f, 0x6d, 0x53, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x28, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x54, 0x78, 0x46, 0x72, 0x6f, 0x6d,
	0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x22, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x78, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x64, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x55, 0x74, 0x78, 0x6f,
	0x54, 0x78, 0x46, 0x72, 0x6f, 0x6d, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x28, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x2e, 0x54, 0x78, 0x46, 0x72, 0x6f, 0x6d, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x64, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x55, 0x74, 0x78,
	0x6f, 0x54, 0x78, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6f, 0x0a, 0x15, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e,
	0x65, 0x64, 0x54, 0x78, 0x12, 0x2d, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x78, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x69, 0x67, 0x6e,
	0x65, 0x64, 0x54, 0x78, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x64, 0x0a, 0x0f,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x78, 0x12,
	0x27, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54,
	0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x78, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x69, 0x0a, 0x12, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x74, 0x78, 0x6f,
	0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x78, 0x12, 0x2a, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x55, 0x74, 0x78, 0x6f, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x78, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x54, 0x78, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5b, 0x0a,
	0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x74, 0x78, 0x6f, 0x54, 0x78, 0x12, 0x24, 0x2e,
	0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x74, 0x78, 0x6f, 0x54, 0x78, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x74, 0x78, 0x6f,
	0x54, 0x78, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x68, 0x0a, 0x15, 0x76, 0x65,
	0x72, 0x69, 0x66, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x65,
	0x64, 0x54, 0x78, 0x12, 0x26, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x53, 0x69, 0x67, 0x6e,
	0x65, 0x64, 0x54, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x64, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x79, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x78, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x65, 0x0a, 0x12, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x55, 0x74,
	0x78, 0x6f, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x78, 0x12, 0x26, 0x2e, 0x64, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x56, 0x65, 0x72,
	0x69, 0x66, 0x79, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x27, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x53, 0x69, 0x67, 0x6e, 0x65,
	0x64, 0x54, 0x78, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5b, 0x0a, 0x0c, 0x41,
	0x42, 0x49, 0x42, 0x69, 0x6e, 0x54, 0x6f, 0x4a, 0x53, 0x4f, 0x4e, 0x12, 0x24, 0x2e, 0x64, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x41, 0x42,
	0x49, 0x42, 0x69, 0x6e, 0x54, 0x6f, 0x4a, 0x53, 0x4f, 0x4e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x25, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x2e, 0x41, 0x42, 0x49, 0x42, 0x69, 0x6e, 0x54, 0x6f, 0x4a, 0x53, 0x4f, 0x4e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5b, 0x0a, 0x0c, 0x41, 0x42, 0x49, 0x4a,
	0x53, 0x4f, 0x4e, 0x54, 0x6f, 0x42, 0x69, 0x6e, 0x12, 0x24, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x41, 0x42, 0x49, 0x4a, 0x53,
	0x4f, 0x4e, 0x54, 0x6f, 0x42, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25,
	0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x2e, 0x41, 0x42, 0x49, 0x4a, 0x53, 0x4f, 0x4e, 0x54, 0x6f, 0x42, 0x69, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x59, 0x0a, 0x10, 0x67, 0x65, 0x74, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x42, 0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x21, 0x2e, 0x64, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x64,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x5f, 0x0a, 0x18, 0x67, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x53, 0x61, 0x66,
	0x65, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x2e, 0x64,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x42,
	0x61, 0x73, 0x69, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x64, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x42, 0x6c,
	0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x64, 0x0a, 0x1d, 0x67, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x46, 0x69,
	0x6e, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x1d, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x73, 0x69, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x24, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x67, 0x0a, 0x14, 0x67, 0x65, 0x74, 0x42, 0x6c,
	0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x79, 0x48, 0x61, 0x73, 0x68, 0x12,
	0x29, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x79, 0x48,
	0x61, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x64, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x5e, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x42, 0x79, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x12, 0x24, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x42, 0x79, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x64, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x42, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x67, 0x0a, 0x12, 0x67, 0x65, 0x74, 0x54, 0x78, 0x52, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74,
	0x42, 0x79, 0x48, 0x61, 0x73, 0x68, 0x12, 0x27, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x54, 0x78, 0x52, 0x65, 0x63, 0x65, 0x69,
	0x70, 0x74, 0x42, 0x79, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x28, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x2e, 0x54, 0x78, 0x52, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x42, 0x79, 0x48, 0x61, 0x73,
	0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5b, 0x0a, 0x0e, 0x67, 0x65, 0x74,
	0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x48, 0x61, 0x73, 0x68, 0x12, 0x23, 0x2e, 0x64, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x53, 0x74,
	0x6f, 0x72, 0x61, 0x67, 0x65, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x24, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x58, 0x0a, 0x0d, 0x67, 0x65, 0x74, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x73, 0x12, 0x22, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x64, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x6a, 0x0a, 0x13, 0x67, 0x65, 0x74, 0x54, 0x78, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x28, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x54, 0x78, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x42, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x29, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x2e, 0x54, 0x78, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x67, 0x0a, 0x12,
	0x67, 0x65, 0x74, 0x53, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x47, 0x61, 0x73, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x12, 0x27, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x53, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x47, 0x61, 0x73, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x64, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x53, 0x75,
	0x67, 0x67, 0x65, 0x73, 0x74, 0x47, 0x61, 0x73, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x68, 0x0a, 0x13, 0x67, 0x65, 0x74, 0x53, 0x75, 0x67, 0x67,
	0x65, 0x73, 0x74, 0x47, 0x61, 0x73, 0x54, 0x69, 0x70, 0x43, 0x61, 0x70, 0x12, 0x27, 0x2e, 0x64,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x53,
	0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x47, 0x61, 0x73, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x53, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x47,
	0x61, 0x73, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42,
	0x25, 0x0a, 0x13, 0x78, 0x79, 0x7a, 0x2e, 0x64, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x5a, 0x0e, 0x2e, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_dapplink_wallet_proto_rawDescOnce sync.Once
	file_dapplink_wallet_proto_rawDescData []byte
)

func file_dapplink_wallet_proto_rawDescGZIP() []byte {
	file_dapplink_wallet_proto_rawDescOnce.Do(func() {
		file_dapplink_wallet_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_dapplink_wallet_proto_rawDesc), len(file_dapplink_wallet_proto_rawDesc)))
	})
	return file_dapplink_wallet_proto_rawDescData
}

var file_dapplink_wallet_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_dapplink_wallet_proto_msgTypes = make([]protoimpl.MessageInfo, 75)
var file_dapplink_wallet_proto_goTypes = []any{
	(TxStatus)(0),                        // 0: dapplink.wallet.TxStatus
	(*Address)(nil),                      // 1: dapplink.wallet.Address
	(*Value)(nil),                        // 2: dapplink.wallet.Value
	(*TxMessage)(nil),                    // 3: dapplink.wallet.TxMessage
	(*Vin)(nil),                          // 4: dapplink.wallet.Vin
	(*Vout)(nil),                         // 5: dapplink.wallet.Vout
	(*SupportCoinsRequest)(nil),          // 6: dapplink.wallet.SupportCoinsRequest
	(*SupportCoinsResponse)(nil),         // 7: dapplink.wallet.SupportCoinsResponse
	(*ConvertAddressRequest)(nil),        // 8: dapplink.wallet.ConvertAddressRequest
	(*ConvertAddressResponse)(nil),       // 9: dapplink.wallet.ConvertAddressResponse
	(*ValidAddressRequest)(nil),          // 10: dapplink.wallet.ValidAddressRequest
	(*ValidAddressResponse)(nil),         // 11: dapplink.wallet.ValidAddressResponse
	(*NonceRequest)(nil),                 // 12: dapplink.wallet.NonceRequest
	(*NonceResponse)(nil),                // 13: dapplink.wallet.NonceResponse
	(*GasPriceRequest)(nil),              // 14: dapplink.wallet.GasPriceRequest
	(*GasPriceResponse)(nil),             // 15: dapplink.wallet.GasPriceResponse
	(*BalanceRequest)(nil),               // 16: dapplink.wallet.BalanceRequest
	(*BalanceResponse)(nil),              // 17: dapplink.wallet.BalanceResponse
	(*AccountRequest)(nil),               // 18: dapplink.wallet.AccountRequest
	(*AccountResponse)(nil),              // 19: dapplink.wallet.AccountResponse
	(*MinRentRequest)(nil),               // 20: dapplink.wallet.MinRentRequest
	(*MinRentResponse)(nil),              // 21: dapplink.wallet.MinRentResponse
	(*SendTxRequest)(nil),                // 22: dapplink.wallet.SendTxRequest
	(*SendTxResponse)(nil),               // 23: dapplink.wallet.SendTxResponse
	(*TxAddressRequest)(nil),             // 24: dapplink.wallet.TxAddressRequest
	(*TxAddressResponse)(nil),            // 25: dapplink.wallet.TxAddressResponse
	(*TxHashRequest)(nil),                // 26: dapplink.wallet.TxHashRequest
	(*TxHashResponse)(nil),               // 27: dapplink.wallet.TxHashResponse
	(*UtxoRequest)(nil),                  // 28: dapplink.wallet.UtxoRequest
	(*UtxoResponse)(nil),                 // 29: dapplink.wallet.UtxoResponse
	(*UnspentOutput)(nil),                // 30: dapplink.wallet.UnspentOutput
	(*UnspentOutputsRequest)(nil),        // 31: dapplink.wallet.UnspentOutputsRequest
	(*UnspentOutputsResponse)(nil),       // 32: dapplink.wallet.UnspentOutputsResponse
	(*UtxoInsFromDataRequest)(nil),       // 33: dapplink.wallet.UtxoInsFromDataRequest
	(*UtxoInsResponse)(nil),              // 34: dapplink.wallet.UtxoInsResponse
	(*TxFromDataRequest)(nil),            // 35: dapplink.wallet.TxFromDataRequest
	(*AccountTxResponse)(nil),            // 36: dapplink.wallet.AccountTxResponse
	(*UtxoTxResponse)(nil),               // 37: dapplink.wallet.UtxoTxResponse
	(*TxFromSignedDataRequest)(nil),      // 38: dapplink.wallet.TxFromSignedDataRequest
	(*CreateAccountSignedTxRequest)(nil), // 39: dapplink.wallet.CreateAccountSignedTxRequest
	(*CreateSignedTxResponse)(nil),       // 40: dapplink.wallet.CreateSignedTxResponse
	(*CreateAccountTxRequest)(nil),       // 41: dapplink.wallet.CreateAccountTxRequest
	(*CreateAccountTxResponse)(nil),      // 42: dapplink.wallet.CreateAccountTxResponse
	(*CreateUtxoSignedTxRequest)(nil),    // 43: dapplink.wallet.CreateUtxoSignedTxRequest
	(*CreateUtxoTxRequest)(nil),          // 44: dapplink.wallet.CreateUtxoTxRequest
	(*CreateUtxoTxResponse)(nil),         // 45: dapplink.wallet.CreateUtxoTxResponse
	(*VerifySignedTxRequest)(nil),        // 46: dapplink.wallet.VerifySignedTxRequest
	(*VerifySignedTxResponse)(nil),       // 47: dapplink.wallet.VerifySignedTxResponse
	(*ABIBinToJSONRequest)(nil),          // 48: dapplink.wallet.ABIBinToJSONRequest
	(*ABIBinToJSONResponse)(nil),         // 49: dapplink.wallet.ABIBinToJSONResponse
	(*ABIJSONToBinRequest)(nil),          // 50: dapplink.wallet.ABIJSONToBinRequest
	(*ABIJSONToBinResponse)(nil),         // 51: dapplink.wallet.ABIJSONToBinResponse
	(*BlockRequest)(nil),                 // 52: dapplink.wallet.BlockRequest
	(*BlockResponse)(nil),                // 53: dapplink.wallet.BlockResponse
	(*BlockInfoRequest)(nil),             // 54: dapplink.wallet.BlockInfoRequest
	(*BlockInfoResponse)(nil),            // 55: dapplink.wallet.BlockInfoResponse
	(*BlockInfoTransactionList)(nil),     // 56: dapplink.wallet.BlockInfoTransactionList
	(*BlockHeaderRequest)(nil),           // 57: dapplink.wallet.BlockHeaderRequest
	(*BlockHeaderResponse)(nil),          // 58: dapplink.wallet.BlockHeaderResponse
	(*LatestSafeBlockHeaderRequest)(nil), // 59: dapplink.wallet.LatestSafeBlockHeaderRequest
	(*BasicRequest)(nil),                 // 60: dapplink.wallet.BasicRequest
	(*BlockHeaderByHashRequest)(nil),     // 61: dapplink.wallet.BlockHeaderByHashRequest
	(*BlockByRangeRequest)(nil),          // 62: dapplink.wallet.BlockByRangeRequest
	(*BlockData)(nil),                    // 63: dapplink.wallet.BlockData
	(*BlockByRangeResponse)(nil),         // 64: dapplink.wallet.BlockByRangeResponse
	(*TxReceiptByHashRequest)(nil),       // 65: dapplink.wallet.TxReceiptByHashRequest
	(*TxReceiptByHashResponse)(nil),      // 66: dapplink.wallet.TxReceiptByHashResponse
	(*StorageHashRequest)(nil),           // 67: dapplink.wallet.StorageHashRequest
	(*StorageHashResponse)(nil),          // 68: dapplink.wallet.StorageHashResponse
	(*FilterLogsRequest)(nil),            // 69: dapplink.wallet.FilterLogsRequest
	(*Log)(nil),                          // 70: dapplink.wallet.Log
	(*FilterLogsResponse)(nil),           // 71: dapplink.wallet.FilterLogsResponse
	(*TxCountByAddressRequest)(nil),      // 72: dapplink.wallet.TxCountByAddressRequest
	(*TxCountByAddressResponse)(nil),     // 73: dapplink.wallet.TxCountByAddressResponse
	(*SuggestGasPriceRequest)(nil),       // 74: dapplink.wallet.SuggestGasPriceRequest
	(*SuggestGasPriceResponse)(nil),      // 75: dapplink.wallet.SuggestGasPriceResponse
	(common.ReturnCode)(0),               // 76: dapplink.ReturnCode
}
var file_dapplink_wallet_proto_depIdxs = []int32{
	1,  // 0: dapplink.wallet.TxMessage.froms:type_name -> dapplink.wallet.Address
	1,  // 1: dapplink.wallet.TxMessage.tos:type_name -> dapplink.wallet.Address
	2,  // 2: dapplink.wallet.TxMessage.values:type_name -> dapplink.wallet.Value
	0,  // 3: dapplink.wallet.TxMessage.status:type_name -> dapplink.wallet.TxStatus
	76, // 4: dapplink.wallet.SupportCoinsResponse.code:type_name -> dapplink.ReturnCode
	76, // 5: dapplink.wallet.ConvertAddressResponse.code:type_name -> dapplink.ReturnCode
	76, // 6: dapplink.wallet.ValidAddressResponse.code:type_name -> dapplink.ReturnCode
	76, // 7: dapplink.wallet.NonceResponse.code:type_name -> dapplink.ReturnCode
	76, // 8: dapplink.wallet.GasPriceResponse.code:type_name -> dapplink.ReturnCode
	76, // 9: dapplink.wallet.BalanceResponse.code:type_name -> dapplink.ReturnCode
	76, // 10: dapplink.wallet.AccountResponse.code:type_name -> dapplink.ReturnCode
	76, // 11: dapplink.wallet.MinRentResponse.code:type_name -> dapplink.ReturnCode
	76, // 12: dapplink.wallet.SendTxResponse.code:type_name -> dapplink.ReturnCode
	76, // 13: dapplink.wallet.TxAddressResponse.code:type_name -> dapplink.ReturnCode
	3,  // 14: dapplink.wallet.TxAddressResponse.tx:type_name -> dapplink.wallet.TxMessage
	76, // 15: dapplink.wallet.TxHashResponse.code:type_name -> dapplink.ReturnCode
	3,  // 16: dapplink.wallet.TxHashResponse.tx:type_name -> dapplink.wallet.TxMessage
	4,  // 17: dapplink.wallet.UtxoRequest.vin:type_name -> dapplink.wallet.Vin
	76, // 18: dapplink.wallet.UtxoResponse.code:type_name -> dapplink.ReturnCode
	76, // 19: dapplink.wallet.UnspentOutputsResponse.code:type_name -> dapplink.ReturnCode
	30, // 20: dapplink.wallet.UnspentOutputsResponse.unspent_outputs:type_name -> dapplink.wallet.UnspentOutput
	76, // 21: dapplink.wallet.UtxoInsResponse.code:type_name -> dapplink.ReturnCode
	4,  // 22: dapplink.wallet.UtxoInsResponse.vins:type_name -> dapplink.wallet.Vin
	4,  // 23: dapplink.wallet.TxFromDataRequest.vins:type_name -> dapplink.wallet.Vin
	76, // 24: dapplink.wallet.AccountTxResponse.code:type_name -> dapplink.ReturnCode
	0,  // 25: dapplink.wallet.AccountTxResponse.status:type_name -> dapplink.wallet.TxStatus
	76, // 26: dapplink.wallet.UtxoTxResponse.code:type_name -> dapplink.ReturnCode
	0,  // 27: dapplink.wallet.UtxoTxResponse.status:type_name -> dapplink.wallet.TxStatus
	4,  // 28: dapplink.wallet.UtxoTxResponse.vins:type_name -> dapplink.wallet.Vin
	5,  // 29: dapplink.wallet.UtxoTxResponse.vouts:type_name -> dapplink.wallet.Vout
	4,  // 30: dapplink.wallet.TxFromSignedDataRequest.vins:type_name -> dapplink.wallet.Vin
	76, // 31: dapplink.wallet.CreateSignedTxResponse.code:type_name -> dapplink.ReturnCode
	76, // 32: dapplink.wallet.CreateAccountTxResponse.code:type_name -> dapplink.ReturnCode
	4,  // 33: dapplink.wallet.CreateUtxoTxRequest.vins:type_name -> dapplink.wallet.Vin
	5,  // 34: dapplink.wallet.CreateUtxoTxRequest.vouts:type_name -> dapplink.wallet.Vout
	76, // 35: dapplink.wallet.CreateUtxoTxResponse.code:type_name -> dapplink.ReturnCode
	4,  // 36: dapplink.wallet.VerifySignedTxRequest.vins:type_name -> dapplink.wallet.Vin
	76, // 37: dapplink.wallet.VerifySignedTxResponse.code:type_name -> dapplink.ReturnCode
	76, // 38: dapplink.wallet.ABIBinToJSONResponse.code:type_name -> dapplink.ReturnCode
	76, // 39: dapplink.wallet.ABIJSONToBinResponse.code:type_name -> dapplink.ReturnCode
	76, // 40: dapplink.wallet.BlockResponse.code:type_name -> dapplink.ReturnCode
	76, // 41: dapplink.wallet.BlockInfoResponse.code:type_name -> dapplink.ReturnCode
	56, // 42: dapplink.wallet.BlockInfoResponse.transactions:type_name -> dapplink.wallet.BlockInfoTransactionList
	76, // 43: dapplink.wallet.BlockHeaderResponse.code:type_name -> dapplink.ReturnCode
	3,  // 44: dapplink.wallet.BlockData.transactions:type_name -> dapplink.wallet.TxMessage
	76, // 45: dapplink.wallet.BlockByRangeResponse.code:type_name -> dapplink.ReturnCode
	63, // 46: dapplink.wallet.BlockByRangeResponse.Blocks:type_name -> dapplink.wallet.BlockData
	76, // 47: dapplink.wallet.TxReceiptByHashResponse.code:type_name -> dapplink.ReturnCode
	76, // 48: dapplink.wallet.StorageHashResponse.code:type_name -> dapplink.ReturnCode
	76, // 49: dapplink.wallet.FilterLogsResponse.code:type_name -> dapplink.ReturnCode
	58, // 50: dapplink.wallet.FilterLogsResponse.toBlockHeader:type_name -> dapplink.wallet.BlockHeaderResponse
	70, // 51: dapplink.wallet.FilterLogsResponse.logs:type_name -> dapplink.wallet.Log
	76, // 52: dapplink.wallet.TxCountByAddressResponse.code:type_name -> dapplink.ReturnCode
	76, // 53: dapplink.wallet.SuggestGasPriceResponse.code:type_name -> dapplink.ReturnCode
	6,  // 54: dapplink.wallet.WalletService.getSupportCoins:input_type -> dapplink.wallet.SupportCoinsRequest
	8,  // 55: dapplink.wallet.WalletService.convertAddress:input_type -> dapplink.wallet.ConvertAddressRequest
	10, // 56: dapplink.wallet.WalletService.validAddress:input_type -> dapplink.wallet.ValidAddressRequest
	52, // 57: dapplink.wallet.WalletService.getBlock:input_type -> dapplink.wallet.BlockRequest
	57, // 58: dapplink.wallet.WalletService.getBlockHeaderByNumber:input_type -> dapplink.wallet.BlockHeaderRequest
	12, // 59: dapplink.wallet.WalletService.getNonce:input_type -> dapplink.wallet.NonceRequest
	14, // 60: dapplink.wallet.WalletService.getGasPrice:input_type -> dapplink.wallet.GasPriceRequest
	16, // 61: dapplink.wallet.WalletService.getBalance:input_type -> dapplink.wallet.BalanceRequest
	18, // 62: dapplink.wallet.WalletService.getAccount:input_type -> dapplink.wallet.AccountRequest
	20, // 63: dapplink.wallet.WalletService.getMinRent:input_type -> dapplink.wallet.MinRentRequest
	22, // 64: dapplink.wallet.WalletService.SendTx:input_type -> dapplink.wallet.SendTxRequest
	24, // 65: dapplink.wallet.WalletService.getTxByAddress:input_type -> dapplink.wallet.TxAddressRequest
	26, // 66: dapplink.wallet.WalletService.getTxByHash:input_type -> dapplink.wallet.TxHashRequest
	28, // 67: dapplink.wallet.WalletService.getUtxo:input_type -> dapplink.wallet.UtxoRequest
	31, // 68: dapplink.wallet.WalletService.getUnspentOutputs:input_type -> dapplink.wallet.UnspentOutputsRequest
	33, // 69: dapplink.wallet.WalletService.getUtxoInsFromData:input_type -> dapplink.wallet.UtxoInsFromDataRequest
	35, // 70: dapplink.wallet.WalletService.getAccountTxFromData:input_type -> dapplink.wallet.TxFromDataRequest
	35, // 71: dapplink.wallet.WalletService.getUtxoTxFromData:input_type -> dapplink.wallet.TxFromDataRequest
	38, // 72: dapplink.wallet.WalletService.getAccountTxFromSignedData:input_type -> dapplink.wallet.TxFromSignedDataRequest
	38, // 73: dapplink.wallet.WalletService.GetUtxoTxFromSignedData:input_type -> dapplink.wallet.TxFromSignedDataRequest
	39, // 74: dapplink.wallet.WalletService.createAccountSignedTx:input_type -> dapplink.wallet.CreateAccountSignedTxRequest
	41, // 75: dapplink.wallet.WalletService.createAccountTx:input_type -> dapplink.wallet.CreateAccountTxRequest
	43, // 76: dapplink.wallet.WalletService.createUtxoSignedTx:input_type -> dapplink.wallet.CreateUtxoSignedTxRequest
	44, // 77: dapplink.wallet.WalletService.createUtxoTx:input_type -> dapplink.wallet.CreateUtxoTxRequest
	46, // 78: dapplink.wallet.WalletService.verifyAccountSignedTx:input_type -> dapplink.wallet.VerifySignedTxRequest
	46, // 79: dapplink.wallet.WalletService.verifyUtxoSignedTx:input_type -> dapplink.wallet.VerifySignedTxRequest
	48, // 80: dapplink.wallet.WalletService.ABIBinToJSON:input_type -> dapplink.wallet.ABIBinToJSONRequest
	50, // 81: dapplink.wallet.WalletService.ABIJSONToBin:input_type -> dapplink.wallet.ABIJSONToBinRequest
	54, // 82: dapplink.wallet.WalletService.getBlockByNumber:input_type -> dapplink.wallet.BlockInfoRequest
	60, // 83: dapplink.wallet.WalletService.getLatestSafeBlockHeader:input_type -> dapplink.wallet.BasicRequest
	60, // 84: dapplink.wallet.WalletService.getLatestFinalizedBlockHeader:input_type -> dapplink.wallet.BasicRequest
	61, // 85: dapplink.wallet.WalletService.getBlockHeaderByHash:input_type -> dapplink.wallet.BlockHeaderByHashRequest
	62, // 86: dapplink.wallet.WalletService.GetBlockByRange:input_type -> dapplink.wallet.BlockByRangeRequest
	65, // 87: dapplink.wallet.WalletService.getTxReceiptByHash:input_type -> dapplink.wallet.TxReceiptByHashRequest
	67, // 88: dapplink.wallet.WalletService.getStorageHash:input_type -> dapplink.wallet.StorageHashRequest
	69, // 89: dapplink.wallet.WalletService.getFilterLogs:input_type -> dapplink.wallet.FilterLogsRequest
	72, // 90: dapplink.wallet.WalletService.getTxCountByAddress:input_type -> dapplink.wallet.TxCountByAddressRequest
	74, // 91: dapplink.wallet.WalletService.getSuggestGasPrice:input_type -> dapplink.wallet.SuggestGasPriceRequest
	74, // 92: dapplink.wallet.WalletService.getSuggestGasTipCap:input_type -> dapplink.wallet.SuggestGasPriceRequest
	7,  // 93: dapplink.wallet.WalletService.getSupportCoins:output_type -> dapplink.wallet.SupportCoinsResponse
	9,  // 94: dapplink.wallet.WalletService.convertAddress:output_type -> dapplink.wallet.ConvertAddressResponse
	11, // 95: dapplink.wallet.WalletService.validAddress:output_type -> dapplink.wallet.ValidAddressResponse
	53, // 96: dapplink.wallet.WalletService.getBlock:output_type -> dapplink.wallet.BlockResponse
	58, // 97: dapplink.wallet.WalletService.getBlockHeaderByNumber:output_type -> dapplink.wallet.BlockHeaderResponse
	13, // 98: dapplink.wallet.WalletService.getNonce:output_type -> dapplink.wallet.NonceResponse
	15, // 99: dapplink.wallet.WalletService.getGasPrice:output_type -> dapplink.wallet.GasPriceResponse
	17, // 100: dapplink.wallet.WalletService.getBalance:output_type -> dapplink.wallet.BalanceResponse
	19, // 101: dapplink.wallet.WalletService.getAccount:output_type -> dapplink.wallet.AccountResponse
	21, // 102: dapplink.wallet.WalletService.getMinRent:output_type -> dapplink.wallet.MinRentResponse
	23, // 103: dapplink.wallet.WalletService.SendTx:output_type -> dapplink.wallet.SendTxResponse
	25, // 104: dapplink.wallet.WalletService.getTxByAddress:output_type -> dapplink.wallet.TxAddressResponse
	27, // 105: dapplink.wallet.WalletService.getTxByHash:output_type -> dapplink.wallet.TxHashResponse
	29, // 106: dapplink.wallet.WalletService.getUtxo:output_type -> dapplink.wallet.UtxoResponse
	32, // 107: dapplink.wallet.WalletService.getUnspentOutputs:output_type -> dapplink.wallet.UnspentOutputsResponse
	34, // 108: dapplink.wallet.WalletService.getUtxoInsFromData:output_type -> dapplink.wallet.UtxoInsResponse
	36, // 109: dapplink.wallet.WalletService.getAccountTxFromData:output_type -> dapplink.wallet.AccountTxResponse
	37, // 110: dapplink.wallet.WalletService.getUtxoTxFromData:output_type -> dapplink.wallet.UtxoTxResponse
	36, // 111: dapplink.wallet.WalletService.getAccountTxFromSignedData:output_type -> dapplink.wallet.AccountTxResponse
	37, // 112: dapplink.wallet.WalletService.GetUtxoTxFromSignedData:output_type -> dapplink.wallet.UtxoTxResponse
	40, // 113: dapplink.wallet.WalletService.createAccountSignedTx:output_type -> dapplink.wallet.CreateSignedTxResponse
	42, // 114: dapplink.wallet.WalletService.createAccountTx:output_type -> dapplink.wallet.CreateAccountTxResponse
	40, // 115: dapplink.wallet.WalletService.createUtxoSignedTx:output_type -> dapplink.wallet.CreateSignedTxResponse
	45, // 116: dapplink.wallet.WalletService.createUtxoTx:output_type -> dapplink.wallet.CreateUtxoTxResponse
	47, // 117: dapplink.wallet.WalletService.verifyAccountSignedTx:output_type -> dapplink.wallet.VerifySignedTxResponse
	47, // 118: dapplink.wallet.WalletService.verifyUtxoSignedTx:output_type -> dapplink.wallet.VerifySignedTxResponse
	49, // 119: dapplink.wallet.WalletService.ABIBinToJSON:output_type -> dapplink.wallet.ABIBinToJSONResponse
	51, // 120: dapplink.wallet.WalletService.ABIJSONToBin:output_type -> dapplink.wallet.ABIJSONToBinResponse
	55, // 121: dapplink.wallet.WalletService.getBlockByNumber:output_type -> dapplink.wallet.BlockInfoResponse
	58, // 122: dapplink.wallet.WalletService.getLatestSafeBlockHeader:output_type -> dapplink.wallet.BlockHeaderResponse
	58, // 123: dapplink.wallet.WalletService.getLatestFinalizedBlockHeader:output_type -> dapplink.wallet.BlockHeaderResponse
	58, // 124: dapplink.wallet.WalletService.getBlockHeaderByHash:output_type -> dapplink.wallet.BlockHeaderResponse
	64, // 125: dapplink.wallet.WalletService.GetBlockByRange:output_type -> dapplink.wallet.BlockByRangeResponse
	66, // 126: dapplink.wallet.WalletService.getTxReceiptByHash:output_type -> dapplink.wallet.TxReceiptByHashResponse
	68, // 127: dapplink.wallet.WalletService.getStorageHash:output_type -> dapplink.wallet.StorageHashResponse
	71, // 128: dapplink.wallet.WalletService.getFilterLogs:output_type -> dapplink.wallet.FilterLogsResponse
	73, // 129: dapplink.wallet.WalletService.getTxCountByAddress:output_type -> dapplink.wallet.TxCountByAddressResponse
	75, // 130: dapplink.wallet.WalletService.getSuggestGasPrice:output_type -> dapplink.wallet.SuggestGasPriceResponse
	75, // 131: dapplink.wallet.WalletService.getSuggestGasTipCap:output_type -> dapplink.wallet.SuggestGasPriceResponse
	93, // [93:132] is the sub-list for method output_type
	54, // [54:93] is the sub-list for method input_type
	54, // [54:54] is the sub-list for extension type_name
	54, // [54:54] is the sub-list for extension extendee
	0,  // [0:54] is the sub-list for field type_name
}

func init() { file_dapplink_wallet_proto_init() }
func file_dapplink_wallet_proto_init() {
	if File_dapplink_wallet_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_dapplink_wallet_proto_rawDesc), len(file_dapplink_wallet_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   75,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_dapplink_wallet_proto_goTypes,
		DependencyIndexes: file_dapplink_wallet_proto_depIdxs,
		EnumInfos:         file_dapplink_wallet_proto_enumTypes,
		MessageInfos:      file_dapplink_wallet_proto_msgTypes,
	}.Build()
	File_dapplink_wallet_proto = out.File
	file_dapplink_wallet_proto_goTypes = nil
	file_dapplink_wallet_proto_depIdxs = nil
}
