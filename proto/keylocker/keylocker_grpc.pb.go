// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v4.25.3
// source: dapplink/keylocker.proto

package keylocker

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	LeyLockerService_GetSupportChain_FullMethodName = "/dapplink.keylocker.LeyLockerService/getSupportChain"
	LeyLockerService_SetSocialKey_FullMethodName    = "/dapplink.keylocker.LeyLockerService/setSocialKey"
	LeyLockerService_GetSocialKey_FullMethodName    = "/dapplink.keylocker.LeyLockerService/getSocialKey"
)

// LeyLockerServiceClient is the client API for LeyLockerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LeyLockerServiceClient interface {
	GetSupportChain(ctx context.Context, in *SupportChainReq, opts ...grpc.CallOption) (*SupportChainRep, error)
	SetSocialKey(ctx context.Context, in *SetSocialKeyReq, opts ...grpc.CallOption) (*SetSocialKeyRep, error)
	GetSocialKey(ctx context.Context, in *GetSocialKeyReq, opts ...grpc.CallOption) (*GetSocialKeyRep, error)
}

type leyLockerServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLeyLockerServiceClient(cc grpc.ClientConnInterface) LeyLockerServiceClient {
	return &leyLockerServiceClient{cc}
}

func (c *leyLockerServiceClient) GetSupportChain(ctx context.Context, in *SupportChainReq, opts ...grpc.CallOption) (*SupportChainRep, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SupportChainRep)
	err := c.cc.Invoke(ctx, LeyLockerService_GetSupportChain_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *leyLockerServiceClient) SetSocialKey(ctx context.Context, in *SetSocialKeyReq, opts ...grpc.CallOption) (*SetSocialKeyRep, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SetSocialKeyRep)
	err := c.cc.Invoke(ctx, LeyLockerService_SetSocialKey_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *leyLockerServiceClient) GetSocialKey(ctx context.Context, in *GetSocialKeyReq, opts ...grpc.CallOption) (*GetSocialKeyRep, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSocialKeyRep)
	err := c.cc.Invoke(ctx, LeyLockerService_GetSocialKey_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LeyLockerServiceServer is the server API for LeyLockerService service.
// All implementations should embed UnimplementedLeyLockerServiceServer
// for forward compatibility.
type LeyLockerServiceServer interface {
	GetSupportChain(context.Context, *SupportChainReq) (*SupportChainRep, error)
	SetSocialKey(context.Context, *SetSocialKeyReq) (*SetSocialKeyRep, error)
	GetSocialKey(context.Context, *GetSocialKeyReq) (*GetSocialKeyRep, error)
}

// UnimplementedLeyLockerServiceServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedLeyLockerServiceServer struct{}

func (UnimplementedLeyLockerServiceServer) GetSupportChain(context.Context, *SupportChainReq) (*SupportChainRep, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSupportChain not implemented")
}
func (UnimplementedLeyLockerServiceServer) SetSocialKey(context.Context, *SetSocialKeyReq) (*SetSocialKeyRep, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetSocialKey not implemented")
}
func (UnimplementedLeyLockerServiceServer) GetSocialKey(context.Context, *GetSocialKeyReq) (*GetSocialKeyRep, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSocialKey not implemented")
}
func (UnimplementedLeyLockerServiceServer) testEmbeddedByValue() {}

// UnsafeLeyLockerServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LeyLockerServiceServer will
// result in compilation errors.
type UnsafeLeyLockerServiceServer interface {
	mustEmbedUnimplementedLeyLockerServiceServer()
}

func RegisterLeyLockerServiceServer(s grpc.ServiceRegistrar, srv LeyLockerServiceServer) {
	// If the following call pancis, it indicates UnimplementedLeyLockerServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&LeyLockerService_ServiceDesc, srv)
}

func _LeyLockerService_GetSupportChain_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SupportChainReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LeyLockerServiceServer).GetSupportChain(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LeyLockerService_GetSupportChain_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LeyLockerServiceServer).GetSupportChain(ctx, req.(*SupportChainReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LeyLockerService_SetSocialKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetSocialKeyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LeyLockerServiceServer).SetSocialKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LeyLockerService_SetSocialKey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LeyLockerServiceServer).SetSocialKey(ctx, req.(*SetSocialKeyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LeyLockerService_GetSocialKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSocialKeyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LeyLockerServiceServer).GetSocialKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LeyLockerService_GetSocialKey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LeyLockerServiceServer).GetSocialKey(ctx, req.(*GetSocialKeyReq))
	}
	return interceptor(ctx, in, info, handler)
}

// LeyLockerService_ServiceDesc is the grpc.ServiceDesc for LeyLockerService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var LeyLockerService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "dapplink.keylocker.LeyLockerService",
	HandlerType: (*LeyLockerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "getSupportChain",
			Handler:    _LeyLockerService_GetSupportChain_Handler,
		},
		{
			MethodName: "setSocialKey",
			Handler:    _LeyLockerService_SetSocialKey_Handler,
		},
		{
			MethodName: "getSocialKey",
			Handler:    _LeyLockerService_GetSocialKey_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "dapplink/keylocker.proto",
}
