syntax = "proto3";

import "dapplink/common.proto";
option go_package = "./proto/wallet";
option java_package = "xyz.dapplink.wallet";
package dapplink.wallet;

enum TxStatus{
  NotFound = 0;
  Pending = 1;
  Failed = 2;
  Success = 3;
  ContractExecuteFailed = 4;
  Other = 5;
}

message Address {
  string address = 1;
}

message Value {
  string value = 1;
}

message TxMessage {
  string hash = 1;
  uint32 index = 2;
  repeated Address froms = 3;
  repeated Address tos = 4;
  repeated Value values = 7;
  string fee = 5;
  TxStatus status = 6;
  int32 type = 8;
  string height = 9;
  string contract_address = 10;
  string datetime = 11;
}

message Vin{
  string hash = 1;
  uint32 index = 2;
  int64  amount = 3;
  string address = 4;
}

message Vout{
  string address = 1;
  int64 amount = 2;
  uint32 index = 3;
}

message SupportCoinsRequest{
  string consumer_token = 1;
  string chain = 2;
  string network = 3;
}

message SupportCoinsResponse {
  ReturnCode code = 1;
  string msg = 2;
  bool support = 3;
}

message ConvertAddressRequest{
  string consumer_token = 1;
  string chain = 2;
  string network = 3;
  bytes public_key = 4;
}

message ConvertAddressResponse{
  ReturnCode code = 1;
  string msg = 2;
  string address = 3;
}

message ValidAddressRequest{
  string consumer_token = 1;
  string chain = 2;
  string network = 3;
  string symbol = 4;
  string address = 5;
}

message ValidAddressResponse{
  ReturnCode code = 1;
  string msg = 2;
  bool valid = 3;
  bool can_withdrawal = 4;
  string canonical_address = 5;
}

message NonceRequest {
  string consumer_token = 1;
  string chain = 2;
  string coin = 3;
  string address = 4;
  string network = 5;
}

message NonceResponse {
  ReturnCode code = 1;
  string msg = 2;
  string nonce = 3;
}

message GasPriceRequest {
  string consumer_token = 1;
  string chain = 2;
  string coin = 3;
  string network = 4;
  string rawTx = 5;
  string address = 6;
}

message GasPriceResponse {
  ReturnCode code = 1;
  string msg = 2;
  string gas = 3;
}

message BalanceRequest{
  string consumer_token = 1;
  string chain = 2;
  string coin = 3;
  string network = 4;
  string address = 5;
  string contract_address = 6;
  uint64 proposerKeyIndex = 7;
}

message BalanceResponse {
  ReturnCode code = 1;
  string msg = 2;
  string balance = 3;
  uint64 sequenceNumber = 4;
}

message AccountRequest{
  string consumer_token = 1;
  string chain = 2;
  string coin = 3;
  string network = 4;
  string address = 5;
}

message AccountResponse {
  ReturnCode code = 1;
  string msg = 2;
  string account_number = 3;
  string sequence = 4;
  string network = 5;
}

message MinRentRequest{
  string consumer_token = 1;
  string chain = 2;
  string coin = 3;
  string network = 4;
  string address = 5;
}

message MinRentResponse {
  ReturnCode code = 1;
  string msg = 2;
  string value = 3;
}

message SendTxRequest{
  string consumer_token = 1;
  string chain = 2;
  string coin = 3;
  string network = 4;
  string raw_tx = 5;
}

message SendTxResponse {
  ReturnCode code = 1;
  string msg = 2;
  string tx_hash = 3;
}

message TxAddressRequest{
  string consumer_token = 1;
  string chain = 2;
  string coin = 3;
  string network = 4;
  string address = 5;
  string contract_address = 6;
  uint32 page = 7;
  uint32 pagesize = 8;
  string cursor = 9;
}

message TxAddressResponse {
  ReturnCode code = 1;
  string msg = 2;
  repeated TxMessage tx = 3;
}

message TxHashRequest{
  string consumer_token = 1;
  string chain = 2;
  string coin = 3;
  string network = 4;
  string hash = 5;
}

message TxHashResponse {
  ReturnCode code = 1;
  string msg = 2;
  TxMessage tx = 3;
}

message UtxoRequest{
  string consumer_token = 1;
  string chain = 2;
  string symbol = 3;
  string network = 4;
  Vin vin = 5;
}

message UtxoResponse{
  ReturnCode code = 1;
  string msg = 2;
  bool unspent = 3;
}

message UnspentOutput {
  string tx_hash_big_endian = 1;
  string  tx_hash = 2;
  uint64 tx_output_n = 3;
  string  script = 4;
  uint64 value = 5;
  uint64 confirmations = 6;
  uint64 tx_index = 7;
}

message UnspentOutputsRequest{
  string consumer_token = 1;
  string chain = 2;
  string symbol = 3;
  string network = 4;
  string address = 5;
  string cursor = 6;
  uint64 limit = 7;
  string coinType = 8;
}

message UnspentOutputsResponse{
  ReturnCode code = 1;
  string msg = 2;
  repeated UnspentOutput unspent_outputs = 3;
}

message UtxoInsFromDataRequest{
  string consumer_token = 1;
  string chain = 2;
  string symbol = 3;
  string network = 4;
  bytes data = 5;
}

message UtxoInsResponse{
  ReturnCode code = 1;
  string msg = 2;
  repeated Vin vins = 3;
}

message TxFromDataRequest{
  string consumer_token = 1;
  string chain = 2;
  string symbol = 3;
  string network = 4;
  bytes raw_data = 5;
  int64 height = 6;
  repeated Vin vins = 7;
}

message AccountTxResponse{
  ReturnCode code = 1;
  string msg = 2;
  string tx_hash = 3;
  TxStatus status = 4;
  string from = 5;
  string to = 6;
  string amount = 7;
  string memo = 8;
  uint64 nonce = 9;
  string gas_limit = 10;
  string gas_price = 11;
  string cost_fee = 12;
  uint64 block_height = 13;
  uint64 block_time = 14;
  bytes sign_hash = 15;
  string contract_address = 16;
}

message UtxoTxResponse{
  ReturnCode code = 1;
  string msg = 2;
  string tx_hash = 3;
  TxStatus status = 4;
  repeated Vin  vins = 5;
  repeated Vout vouts = 6;
  repeated bytes sign_hashes = 7;
  string cost_fee = 8;
  uint64 block_height = 9;
  uint64 block_time = 10;
}

message TxFromSignedDataRequest{
  string consumer_token = 1;
  string chain = 2;
  string symbol = 3;
  string network = 4;
  bytes signed_tx_data = 5;
  int64 height = 6;
  repeated Vin vins = 7;
}

message CreateAccountSignedTxRequest{
  string consumer_token = 1;
  string chain = 2;
  string symbol = 3;
  string network = 4;
  bytes tx_data = 5;
  bytes signature = 6;
  bytes public_key = 7;
}

message CreateSignedTxResponse{
  ReturnCode code = 1;
  string msg = 2;
  bytes signed_tx_data = 3;
  bytes hash = 4;
}

message CreateAccountTxRequest{
  string consumer_token = 1;
  string chain = 2;
  string symbol = 3;
  string network = 4;
  string from = 5;
  string to = 6;
  string amount = 7;
  string memo = 8;
  string gas_limit = 9;
  string gas_price = 10;
  uint64 nonce = 11;
  string contract_address = 12;
}

message CreateAccountTxResponse{
  ReturnCode code = 1;
  string msg = 2;
  bytes tx_data = 3;
  bytes sign_hash = 4;
}

message CreateUtxoSignedTxRequest{
  string consumer_token = 1;
  string chain = 2;
  string symbol = 3;
  string network = 4;
  bytes tx_data = 5;
  repeated bytes signatures = 6;
  repeated bytes public_keys = 7;
}

message CreateUtxoTxRequest{
  string consumer_token = 1;
  string chain = 2;
  string symbol = 3;
  string network = 4;
  repeated Vin vins = 5;
  repeated Vout vouts = 6;
  string fee = 7;
}

message CreateUtxoTxResponse{
  ReturnCode code = 1;
  string msg = 2;
  bytes tx_data = 3;
  repeated bytes sign_hashes = 4;
}

message VerifySignedTxRequest{
  string consumer_token = 1;
  string chain = 2;
  string symbol = 3;
  string network = 4;
  repeated string addresses = 5[deprecated = true];
  bytes signed_tx_data = 6;
  string sender = 7;        // eth
  int64 height = 8;         // optional, default to latest
  repeated Vin vins = 9;      // btc
}

message VerifySignedTxResponse{
  ReturnCode code = 1;
  string msg = 2;
  bool verified = 3;
}

message ABIBinToJSONRequest {
  string consumer_token = 1;
  string chain = 2;
  string symbol = 3;
  string network = 4;
  string code = 5;
  string action = 6;
  bytes bin = 7;
}

message ABIBinToJSONResponse{
  ReturnCode code = 1;
  string msg = 2;
  string jsonString = 3;
}

message ABIJSONToBinRequest {
  string consumer_token = 1;
  string chain = 2;
  string symbol = 3;
  string network = 4;
  string code = 5;
  string action = 6;
  string jsonString = 7;
}

message ABIJSONToBinResponse{
  ReturnCode code = 1;
  string msg = 2;
  bytes bin = 3;
}

message BlockRequest {
  string consumer_token = 1;
  string chain = 2;
}

message BlockResponse{
  ReturnCode code = 1;
  string msg = 2;
  int64 height = 3;
  string hash = 4;
}


message BlockInfoRequest {
  string chain = 1;
  string height = 2;
}

message BlockInfoResponse{
  ReturnCode code = 1;
  string msg = 2;
  string hash = 3;
  string baseFee = 4;
  repeated BlockInfoTransactionList transactions = 5;
}

message BlockInfoTransactionList {
  string from = 1;
  string to = 2;
  string hash = 3;
  string time = 4;
  string amount = 5;
}

message BlockHeaderRequest{
  string height = 1;
  string chain = 2;
}

message BlockHeaderResponse{
  ReturnCode code = 1;
  string msg = 2;
  string ParentHash = 3;
  string UncleHash = 4;
  string Coinbase = 5;
  string Root = 6;
  string TxHash = 7;
  string ReceiptHash = 8;
  string ParentBeaconRoot = 9;
  string Difficulty = 10;
  string Number = 11;
  uint64 GasLimit = 12;
  uint64 GasUsed = 13;
  uint64 Time = 14;
  string Extra = 15;
  string MixDigest = 16;
  string Nonce = 17;
  string BaseFee = 18;
  string WithdrawalsHash = 19;
  uint64 BlobGasUsed = 20;
  uint64 ExcessBlobGas = 21;
}

message LatestSafeBlockHeaderRequest{
  string chain = 1;
  string network = 2;
}

// 公共请求消息
message BasicRequest{
  string chain = 1;
  string network = 2;
}

message BlockHeaderByHashRequest{
  string chain = 1;
  string network = 2;
  string hash = 3;
}

message BlockByRangeRequest{
  string chain = 1;
  string network = 2;
  string start = 3;
  string end = 4;
}

message BlockData {
  string hash = 1;
  string baseFee = 2;
  repeated TxMessage transactions = 3;
}

message BlockByRangeResponse{
  ReturnCode code = 1;
  string msg = 2;
  repeated BlockData Blocks = 3;
}

message TxReceiptByHashRequest{
  string chain = 1;
  string network = 2;
  string hash = 3;
}

message TxReceiptByHashResponse{
  ReturnCode code = 1;
  string msg = 2;
  uint32 Type = 3;
  string PostState = 4;
  uint64 Status = 5;
  uint64 CumulativeGasUsed = 6;
  uint64 GasUsed = 7;
  string TxHash = 8;
  string ContractAddress = 9;
  string EffectiveGasPrice = 10;
  string BlockHash = 11;
  string BlockNumber = 12;
  uint64 TransactionIndex = 13;
}

message StorageHashRequest{
  string chain = 1;
  string network = 2;
  string address = 3;
  string blockNumber = 4;
}

message StorageHashResponse{
  ReturnCode code = 1;
  string msg = 2;
  string hash = 3;
}

message FilterLogsRequest{
  string chain = 1;
  string network = 2;
}

message Log {
  string address = 1;
  repeated string topics = 2;
  string data = 3;
  string blockNumber = 4;
  string txHash = 5;
  uint64 txIndex = 6;
  string blockHash = 7;
  uint64 index = 8;
  bool removed = 9;
}

message FilterLogsResponse{
  ReturnCode code = 1;
  string msg = 2;
  BlockHeaderResponse  toBlockHeader = 3;
  repeated Log logs = 4;
}

message TxCountByAddressRequest{
  string chain = 1;
  string network = 2;
  string address = 3;
}

message TxCountByAddressResponse{
  ReturnCode code = 1;
  string msg = 2;
  uint64 count = 3;
}

message SuggestGasPriceRequest{
  string chain = 1;
  string network = 2;
}

message SuggestGasPriceResponse{
  ReturnCode code = 1;
  string msg = 2;
  string gasPrice = 3;
}

service WalletService {
  rpc getSupportCoins(SupportCoinsRequest) returns (SupportCoinsResponse) {}

  rpc convertAddress(ConvertAddressRequest) returns(ConvertAddressResponse){}
  rpc validAddress(ValidAddressRequest) returns(ValidAddressResponse){}

  rpc getBlock(BlockRequest) returns (BlockResponse) {}
  rpc getBlockHeaderByNumber(BlockHeaderRequest) returns (BlockHeaderResponse) {}
  rpc getNonce(NonceRequest) returns (NonceResponse) {}
  rpc getGasPrice(GasPriceRequest) returns (GasPriceResponse) {}
  rpc getBalance(BalanceRequest) returns (BalanceResponse) {}
  rpc getAccount(AccountRequest) returns (AccountResponse) {}
  rpc getMinRent(MinRentRequest) returns (MinRentResponse) {}

  rpc SendTx(SendTxRequest) returns (SendTxResponse) {}

  rpc getTxByAddress(TxAddressRequest) returns (TxAddressResponse) {}
  rpc getTxByHash(TxHashRequest) returns (TxHashResponse) {}

  rpc getUtxo(UtxoRequest) returns (UtxoResponse) {}
  rpc getUnspentOutputs(UnspentOutputsRequest) returns (UnspentOutputsResponse) {}

  rpc getUtxoInsFromData(UtxoInsFromDataRequest) returns(UtxoInsResponse);

  rpc getAccountTxFromData(TxFromDataRequest) returns(AccountTxResponse);
  rpc getUtxoTxFromData(TxFromDataRequest) returns(UtxoTxResponse);

  rpc getAccountTxFromSignedData(TxFromSignedDataRequest) returns(AccountTxResponse);
  rpc GetUtxoTxFromSignedData(TxFromSignedDataRequest) returns(UtxoTxResponse);

  rpc createAccountSignedTx(CreateAccountSignedTxRequest) returns(CreateSignedTxResponse);
  rpc createAccountTx(CreateAccountTxRequest) returns(CreateAccountTxResponse);
  rpc createUtxoSignedTx(CreateUtxoSignedTxRequest) returns(CreateSignedTxResponse);
  rpc createUtxoTx(CreateUtxoTxRequest) returns(CreateUtxoTxResponse);

  rpc verifyAccountSignedTx(VerifySignedTxRequest) returns(VerifySignedTxResponse);
  rpc verifyUtxoSignedTx(VerifySignedTxRequest) returns(VerifySignedTxResponse);

  rpc ABIBinToJSON(ABIBinToJSONRequest) returns(ABIBinToJSONResponse);
  rpc ABIJSONToBin(ABIJSONToBinRequest) returns(ABIJSONToBinResponse);
  rpc getBlockByNumber(BlockInfoRequest) returns(BlockInfoResponse);
  rpc getLatestSafeBlockHeader(BasicRequest) returns(BlockHeaderResponse);
  rpc getLatestFinalizedBlockHeader(BasicRequest) returns(BlockHeaderResponse);
  rpc getBlockHeaderByHash(BlockHeaderByHashRequest) returns(BlockHeaderResponse);
  rpc GetBlockByRange(BlockByRangeRequest) returns(BlockByRangeResponse);
  rpc getTxReceiptByHash(TxReceiptByHashRequest) returns(TxReceiptByHashResponse);
  rpc getStorageHash(StorageHashRequest) returns(StorageHashResponse);
  rpc getFilterLogs(FilterLogsRequest) returns(FilterLogsResponse);
  rpc getTxCountByAddress(TxCountByAddressRequest) returns(TxCountByAddressResponse);
  rpc getSuggestGasPrice(SuggestGasPriceRequest) returns(SuggestGasPriceResponse);
  rpc getSuggestGasTipCap(SuggestGasPriceRequest) returns(SuggestGasPriceResponse);
}