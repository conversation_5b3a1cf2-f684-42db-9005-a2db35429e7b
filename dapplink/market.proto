syntax = "proto3";

import "dapplink/common.proto";
option go_package = "./proto/market";
option java_package = "xyz.dapplink.market";
package dapplink.market;

message Exchange {
    string id = 1;
    string name = 2;
    string type = 3;
}

message Asset {
    string id = 1;
    string name = 2;
}

message Symbol {
   string id = 1;
   string name = 2;
   string base = 3;
   string quote = 4;
}

message SymbolPrice {
   string id = 1;
   string name = 2;
   string base = 3;
   string quote = 4;
   string exchange = 5;
   string symbol = 6;
   string buy_price = 7;
   string sell_price = 8;
   string avg_price = 9;
   string usd_price = 10;
   string cny_price = 11;
   string margin = 12;
}

message StableCoin {
   string id = 1;
   string name = 2;
}

message StableCoinPrice {
   string id = 1;
   string name = 2;
   string usd_price = 3;
   string cny_price = 4;
   string margin = 5;
}

message ExchangeRequest {
    string consumer_token = 1;
}

message ExchangeResponse {
  ReturnCode code = 1;
  string msg = 2;
  repeated Exchange exchanges = 3;
}

message AssetRequest {
   string consumer_token = 1;
   string exchange_id = 2;
   bool is_base = 3;
}

message AssetResponse {
   ReturnCode code=1;
   string msg=2;
   repeated Asset assets = 3;
}

message SymbolRequest {
   string consumer_token = 1;
   string exchange_id = 2;
}

message SymbolResponse {
  ReturnCode code=1;
  string msg=2;
  repeated Symbol symbols = 3;
}

message SymbolPriceRequest {
   string consumer_token = 1;
   string exchange_id = 2;
   string symbol_id = 3;
}

message SymbolPriceResponse {
  ReturnCode code = 1;
  string msg = 2;
  repeated SymbolPrice symbol_prices = 3;
}

message StableCoinRequest {
   string consumer_token = 1;
}

message StableCoinResponse {
  ReturnCode code=1;
  string msg=2;
  repeated StableCoin stable_coins = 3;
}

message StableCoinPriceRequest {
   string consumer_token = 1;
   string coin_id = 2;
}

message StableCoinPriceResponse {
  ReturnCode code=1;
  string msg=2;
  repeated StableCoinPrice coin_prices = 3;
}

service PriceService {
  rpc getExchanges(ExchangeRequest) returns (ExchangeResponse) {}
  rpc getAssets(AssetRequest) returns (AssetResponse) {}
  rpc getSymbols(SymbolRequest) returns (SymbolResponse) {}
  rpc getSymbolPrices(SymbolPriceRequest) returns (SymbolPriceResponse) {}
  rpc getStableCoins(StableCoinRequest) returns (StableCoinResponse) {}
  rpc getStableCoinPrice(StableCoinPriceRequest) returns (StableCoinPriceResponse) {}
}



