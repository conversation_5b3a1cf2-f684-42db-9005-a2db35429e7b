syntax = "proto3";

option go_package = "./proto/airdrop";
option java_package = "xyz.dapplink.airdrop";
package services.dapplink.airdrop;


message DppLinkPointsReq {
  string consumer_token = 1;
  string type = 2;
  string address = 3;
}

message DppLinkPointsRep {
  string code=1;
  string msg=2;
}

service AirdropService {
  rpc submitDppLinkPoints(DppLinkPointsReq) returns (DppLinkPointsRep) {}
}




